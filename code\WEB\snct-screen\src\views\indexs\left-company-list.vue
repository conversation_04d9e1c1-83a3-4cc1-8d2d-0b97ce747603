<!--
 * @Author: weizw
 * @Date: 2025-07-28
 * @Description: 企业列表组件
-->
<template>
  <div class="company-list-container" v-if="pageflag">
    <div class="company-list beautify-scroll-def">
      <div
        v-for="(company, index) in companyList"
        :key="company.deptId"
        class="company-item"
        :class="{ 'active': company.status === '1', 'inactive': company.status === '0' }"
        @click="handleCompanyClick(company)"
      >
        <div class="company-index">{{ index + 1 }}</div>
        <div class="company-info">
          <div class="company-name">{{ company.deptName }}</div>
          <div class="company-status">
            <!-- <span class="status-dot" :class="company.status === '1' ? 'online' : 'offline'"></span> -->
            <!-- <span class="status-text">{{ company.status === '1' ? '在线' : '离线' }}</span> -->
          </div>
        </div>
        <!-- <div class="company-id">ID: {{ company.deptId }}</div> -->
      </div>
    </div>
    
    <!-- 无数据时显示 -->
    <div v-if="companyList.length === 0" class="no-data">
      <div class="no-data-text">暂无企业数据</div>
    </div>
  </div>
  
  <!-- 数据加载失败时显示重新获取按钮 -->
  <Reacquire v-else @onclick="handleReconnect" line-height="200px">
    重新获取
  </Reacquire>
</template>

<script>
import { dataModule } from '@/utils/webSocket'

export default {
  name: 'LeftCompanyList',
  data() {
    return {
      companyList: [],
      pageflag: true,
      timer: null,
      wsCheckTimer: null
    }
  },
  created() {
    this.startDataMonitoring()
  },
  mounted() {
    this.checkWebSocketData()
  },
  beforeDestroy() {
    this.clearTimers()
  },
  methods: {
    startDataMonitoring() {
      this.wsCheckTimer = setInterval(() => {
        this.checkWebSocketData()
      }, 1000)
    },
    
    // 检查WebSocket数据
    checkWebSocketData() {
      if (dataModule.D0A01 && Array.isArray(dataModule.D0A01)) {
        const newData = [...dataModule.D0A01]
        if (JSON.stringify(newData) !== JSON.stringify(this.companyList)) {
          this.companyList = newData
          console.log('企业列表数据更新:', this.companyList)
        }
      }
    },
    
    // 处理重新连接请求
    handleReconnect() {
      // 通知父组件重新初始化WebSocket连接
      this.$emit('reconnect')
      this.pageflag = true
    },

    // 处理企业点击事件
    handleCompanyClick(company) {
      console.log('点击企业:', company)

      // 修改dataModule.sendtext
      dataModule.sendtext = `type66#${company.deptId}#0#0B01,0B02,0B03,0B04,0B05,0B06`

      // 将企业信息保存到sessionStorage（关闭浏览器后清除）
      const companyInfo = {
        deptId: company.deptId,
        sendtext: dataModule.sendtext,
        title: company.title,
        timestamp: Date.now(),
        fromPage: 'company-list'
      }
      sessionStorage.setItem('selectedCompany', JSON.stringify(companyInfo))

      console.log('更新后的sendtext:', dataModule.sendtext)

      // 跳转到企业首页
      this.$router.push('/comindex')
    },

    // 清理定时器
    clearTimers() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
      if (this.wsCheckTimer) {
        clearInterval(this.wsCheckTimer)
        this.wsCheckTimer = null
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.company-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.company-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px 0;
  
  .company-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    margin: 8px 2px;
    border-radius: 6px;
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(0, 234, 255, 0.05));
    border: 1px solid rgba(0, 234, 255, 0.2);
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      background: linear-gradient(135deg, rgba(0, 123, 255, 0.2), rgba(0, 234, 255, 0.1));
      border-color: rgba(0, 234, 255, 0.4);
      transform: translateX(2px);
      box-shadow: 0 4px 12px rgba(0, 234, 255, 0.3);
    }
    
    &.active {
      border-color: rgba(7, 247, 168, 0.4);
      background: linear-gradient(135deg, rgba(7, 247, 168, 0.1), rgba(7, 247, 168, 0.05));
    }
    
    &.inactive {
      border-color: rgba(227, 179, 55, 0.4);
      background: linear-gradient(135deg, rgba(227, 179, 55, 0.1), rgba(227, 179, 55, 0.05));
    }
  }
  
  .company-index {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: linear-gradient(135deg, #0072ff, #00eaff);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
    margin-right: 12px;
    flex-shrink: 0;
  }
  
  .company-info {
    flex: 1;
    min-width: 0;
    
    .company-name {
      color: #ffffff;
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 4px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    .company-status {
      display: flex;
      align-items: center;
      
      .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 6px;
        
        &.online {
          background-color: #07f7a8;
          box-shadow: 0 0 6px rgba(7, 247, 168, 0.6);
        }
        
        &.offline {
          background-color: #e3b337;
          box-shadow: 0 0 6px rgba(227, 179, 55, 0.6);
        }
      }
      
      .status-text {
        color: rgba(255, 255, 255, 0.8);
        font-size: 12px;
      }
    }
  }
  
  .company-id {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
    flex-shrink: 0;
  }
}

.no-data {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .no-data-text {
    color: rgba(255, 255, 255, 0.6);
    font-size: 16px;
  }
}

// 滚动条样式
.beautify-scroll-def {
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(0, 234, 255, 0.4);
    border-radius: 3px;
    
    &:hover {
      background: rgba(0, 234, 255, 0.6);
    }
  }
}
</style>
