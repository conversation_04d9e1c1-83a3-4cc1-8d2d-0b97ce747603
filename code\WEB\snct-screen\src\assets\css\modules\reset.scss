/**
 * <PERSON>'s Reset CSS v2.0 (http://meyerweb.com/eric/tools/css/reset/)
 * http://cssreset.com
 */
html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video,
input {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font-weight: normal;
  vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
menu,
nav,
section {
  display: block;
}

body {
  line-height: 1;
}

blockquote,
q {
  quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* custom */

a {
  color: #7e8c8d;
  -webkit-backface-visibility: hidden;
  text-decoration: none;
}

li {
  list-style: none;
}

body {
  -webkit-text-size-adjust: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.olControlScaleLineBottom {
  display: none;
}

.olControlScaleLineTop {
  color: #000 !important;
  border-bottom: solid 3px #000 !important;
  border-left: solid 2px #000 !important;
  border-right: solid 2px #000 !important;
  background-color: rgba(255, 255, 255, .4);
  font-size: 10px;
  text-shadow: -1px -1px 0 #fff, 1px -1px 0 #fff, -1px 1px 0 #fff, 1px 1px 0 #fff;
}

.olControlScaleLine {
  z-index: 900 !important;
}
/*清除浮动*/
.clearfix:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.clearfix {display: inline-block;}
/* 点击搜索框获取焦点 placeholder消失-开始 */
/* WebKit browsers */

// input:focus::-webkit-input-placeholder {
//   color: transparent;
// }


// /* Mozilla Firefox 4 to 18 */

// input:focus:-moz-placeholder {
//   color: transparent;
// }


// /* Mozilla Firefox 19+ */

// input:focus::-moz-placeholder {
//   color: transparent;
// }


// /* Internet Explorer 10+ */

// input:focus:-ms-input-placeholder {
//   color: transparent;
// }

/* 点击搜索框获取焦点 placeholder消失-结束 */
