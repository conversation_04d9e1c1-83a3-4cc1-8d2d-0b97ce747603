<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8' />
    <meta name='viewport' content='initial-scale=1,maximum-scale=1,user-scalable=no'/>
    <link href='http://***************:3000/bigemap.js/v2.1.0/bigemap.css' rel='stylesheet'/>
    <script src='http://***************:3000/bigemap.js/v2.1.0/bigemap.js'></script>
    <style>
        body { margin: 0; padding: 0; }
        #map { position: absolute; top: 0; bottom: 0; width: 100%; }
        .info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(255,255,255,0.9);
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
        }
    </style>
    <title>地图测试页面</title>
</head>
<body>
    <div class="info">
        <h3>地图测试页面</h3>
        <p>如果您能看到地图内容，说明修复成功！</p>
        <button onclick="testAddMarker()">添加测试标记</button>
        <button onclick="testSwitchLayer()">切换海图</button>
    </div>
    <div id='map'></div>
    
    <script>
        let map;
        let satelliteLayer;
        let seaLayer;
        let currentLayer = 'satellite';
        
        // 初始化地图
        function initMap() {
            try {
                // 设置配置
                BM.Config.HTTP_URL = 'http://***************:3000';
                BM.accessToken = 'pk.eyJ1IjoiY3VzXzg3a3J5a3Y4IiwiYSI6IjdmZzdsdWI0eDVtYmF6ZWRoOWxudmt2ZDEifQ.hbJM3hwBW1MVSGg3eiWdNQ';
                
                // 创建地图实例
                map = BM.map('map', 'bigemap.bvgwuwcf', { 
                    center: [24.84957504272461, 118.05908203125], 
                    zoom: 4, 
                    zoomControl: true 
                });
                
                // 创建图层
                satelliteLayer = BM.tileLayer('bigemap.satellite');
                seaLayer = BM.tileLayer('bigemap.seaMap');
                
                // 添加默认图层
                satelliteLayer.addTo(map);
                
                // 设置地图边界
                map.fitBounds([[15.792253494262695, 109.2919921875], [33.90689468383789, 126.826171875]]);
                
                console.log('地图初始化成功！');
            } catch (error) {
                console.error('地图初始化失败:', error);
                alert('地图初始化失败: ' + error.message);
            }
        }
        
        // 添加测试标记
        function testAddMarker() {
            if (map) {
                const marker = BM.marker([24.84957504272461, 118.05908203125])
                    .addTo(map)
                    .bindPopup('这是一个测试标记！');
                console.log('添加标记成功');
            }
        }
        
        // 切换图层
        function testSwitchLayer() {
            if (map) {
                if (currentLayer === 'satellite') {
                    map.removeLayer(satelliteLayer);
                    seaLayer.addTo(map);
                    currentLayer = 'sea';
                    console.log('切换到海图');
                } else {
                    map.removeLayer(seaLayer);
                    satelliteLayer.addTo(map);
                    currentLayer = 'satellite';
                    console.log('切换到卫星图');
                }
            }
        }
        
        // 页面加载完成后初始化地图
        window.onload = function() {
            setTimeout(initMap, 100); // 稍微延迟确保库加载完成
        };
    </script>
</body>
</html>
