{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "serve": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "build": "vue-cli-service build --mode production", "report": "vue-cli-service build --report"}, "dependencies": {"@jiaminghi/data-view": "^2.10.0", "async-validator": "^4.2.5", "axios": "^0.24.0", "crypto-js": "^4.0.0", "echarts": "^5.4.0", "ejs": "^3.1.8", "element-ui": "^2.15.10", "file-saver": "^2.0.5", "follow-redirects": "^1.15.2", "glob-parent": "^6.0.2", "node-forge": "^1.3.1", "sass": "^1.55.0", "uglify-js": "^3.17.4", "vue": "^2.7.13", "vue-easytable": "^2.21.5", "vue-router": "^3.6.5", "vue-seamless-scroll": "^1.1.23", "vuex": "^3.4.0"}, "devDependencies": {"@vue/cli-plugin-router": "^4.5.19", "@vue/cli-plugin-vuex": "^4.5.19", "@vue/cli-service": "^4.5.19", "babel-plugin-component": "^1.1.1", "mockjs": "^1.1.0", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.7.13"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}