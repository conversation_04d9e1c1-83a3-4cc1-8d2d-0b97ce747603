body {
    margin: 0;
    padding: 0;
}

.bigemap-logo {
    display: none;
}

#map {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    padding: 0 0;
}

.tool {
    position: absolute;
    z-index: 10;
    right: 1vw;
    top: 1vw;
}

#control {
    background-color: #eee;
    padding: 15px;
    position: absolute;
    bottom: 2vw;
    right: 1.5vw;
    z-index: 9;
}

.button {
    width: 200px;
    display: inline-block;
}

.ol-popup {
    position: absolute;
    background-color: #91B4C8;
    -webkit-filter: drop-shadow(0 1px 4px rgba(0, 0, 0, 0.2));
    filter: drop-shadow(0 1px 4px rgba(0, 0, 0, 0.2));
    padding: 15px;
    border-radius: 10px;
    border: 1px solid #cccccc;
    bottom: 12px;
    left: -50px;
    min-width: 210px;

}

.leaflet-control-zoom {
    display: none;
}

.leaflet-control-scale-line {
    display: none;
}

/* .leaflet-control-attribution-elane{
    display: none;
} */
.ol-zoom {
    display: none;
}

.ol-popup:after,
.ol-popup:before {
    top: 100%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
}

.ol-popup:after {
    border-top-color: white;
    border-width: 10px;
    left: 48px;
    margin-left: -10px;
}

.ol-popup:before {
    border-top-color: #cccccc;
    border-width: 11px;
    left: 48px;
    margin-left: -11px;
}

.ol-popup-closer {
    text-decoration: none;
    position: absolute;
    top: 2px;
    right: 8px;
    /* background-color: #409DFF; */
}

.ol-popup-closer:after {
    content: "ÃƒÂ¢Ã…â€œÃ¢â‚¬â€œ";
}

.suspendQQ table tbody tr {
    /* border: #286090 solid 1px; */
    color: white;

}

.textB {
    text-align: justify;
    text-justify: distribute-all-lines;
    text-align-last: justify;
    width: 6vw;

}

.suspendQQ table tbody tr td {
    font-size: 1vw;
    /* border: #286090 solid 1px; */
    color: white;
    line-height: 3.2vw;
}

.suspendQQ table {
    margin-left: 1.5vw;
    margin-top: 0.5vw;
    width: 2vw;
    opacity: 0.8;
    /* border: 3px salmon outset ; */
}

.textC {
    text-align: center;
    font-size: 1vw !important;
}

.measuretip {
    position: relative;
    background-color: white;
    opacity: 0.7;
    border-radius: 3px;
    padding: 10px;
    font-size: 12px;
    cursor: default;
}

.vjs-modal-dialog {
    display: none;
}

.vjs-control-bar {
    display: none;
}

.vjs-big-play-button {
    display: none;
}

.back {
    width: 224px;
    position: absolute;
    z-index: 11;
    right: 14px;
    top: 13px;
    background-color: white;
    opacity: 0.7;
    border-radius: 3px;
    padding: 10px;
    font-size: 12px
}

.sailingShow {

    /* z-index: 99; */
    width: 26.6vw;
    height: 57vw;
    top: -0.15vw;
    position: absolute;
    left: 74.5vw;
    /* font-size: 12px; */
    /* border: tomato 1px solid; */
    text-align: center;
    display: none;
}

.imgbg {
    width: 15.5vw;
}

.showbacktext {
    width: 448px;
    height: 105px;
    position: relative;
    top: 320px;
    background-color: #337AB7;
    opacity: 0.8;
    border-radius: 3px;
    right: -4px;
    display: none;
}

.backtext {
    width: 448px;
    height: 105px;
    position: relative;
    top: 320px;
    background-color: white;
    opacity: 0.8;
    border-radius: 3px;
    right: -4px;
}

.btn-group {
    position: absolute;
    top: 0.5vw;
    left: 89.6vw;
}

.huifu {
    background: url(../../img/typhoon/stop.png);
    background-repeat: no-repeat;
    border: none;
    background-size: 32.5px 32.5px;
    margin-left: 9.4px;
    margin-top: 65px;
    z-index: 9;
    height: 32.5px;
    width: 32.5px;
    position: absolute;
}
.backIndex {
    background: url(../../img/typhoon/backindex.png);
    background-repeat: no-repeat;
    border: none;
    background-size: 32.5px 32.5px;
    margin-left: 9.4px;
    margin-top: 65px;
    z-index: 9;
    height: 32.5px;
    width: 32.5px;
    position: absolute;
}
.btn-xss {
    padding: 0.1vw 0.2vw !important;
    font-size: 0.85vw !important;
    line-height: 2vh !important;
    width: 4.6vw !important;
    height: 1.3vw !important;
    border-radius: 0.2vw !important;
    background-color: #0CBFC2 !important;
    /* margin-left: -0.2vw; */
}

#shipText {
    width: 21.5vw;
    /* border: 3px salmon outset ; */
    position: absolute;
    top: 0.5vw;
    /* background-color: white; */
    padding: 0.2vw;
    opacity: 0.8;
    right: 2vw;
    text-align: center;

}

.btnselect {
    /* margin-left:105px; */
    width: 6vw;
    background-color: #0CBFC2;
    margin: 2vw 6.3vw;
}

.inputtime {
    width: 5vw;
    height: 2vw;
    text-align: center;
    font-size: 2vh;
}

.shipsShow {
    width: 252px;
    height: 135px;
    position: absolute;
    z-index: 11;
    left: 80%;
    top: 1%;
    line-height: 20PX;
    background-color: #286090;
    /* opacity: 0.8; */
    border-radius: 3px;
    padding: 10px;
    display: none;
    /* text-align:center; */
}

.gunds {
    width: 16.5vw;
    height: 2.2vw;
    /* margin-left: 3.2vw;
    margin-top: 0.5vw; */
}

.but_sty {
    border: 0px;
    width: 1vw;
    height: 1vw;
    background-color: #333;
    font-size: 12px;
    color: #fff;
}

.buttonGroup {
    margin-left: 4vw;
    width: 100%;
    height: 1.2vw;
    position: absolute;
    top: 37.6vw;
}

.prosty {
    /* border: 1px outset white; */
    float: left;
    width: 6vw;
    height: 2vw;
    margin-left: 1.3vw;
    font-size: 1vw;
}

#backButton {
    cursor: pointer;
    font-size: 1.5vh;
    width: 2vw;
    height: 2vw;
    background-position: center center;
}

#startButton {
    cursor: pointer;
    font-size: 1.5vh;
    /* margin-top: -25px; */
    margin-top: 0.3vw;
    /* width: 4.5vw; */
    width: 2.5vw;
    height: 2.5vw;
    /* margin-right:6vw; */
    /* margin-right:2vw; */
    background-position: center center;
    /* opacity: 0.01; */
    /* background-color: #286090; */

}

#pauseButton {
    cursor: pointer;
    font-size: 1.5vh;
    /* margin-top: 0.5vw; */
    /* margin-right:-0.6vw; */
    /* position: absolute; */
    /* left: 15vw; */
    /* left: 16vw; */
    /* top: 24.88vw; */
    width: 2vw;
    height: 2vw;
    background-position: center center;
    /* opacity: 0.01; */
    /* background-color: #286090; */
}

.seleship {
    width: 8vw;
    height: 2vw;
    text-align: center;
    font-size: 2.2vh;
    margin: 1vw;
    color: black
}

.button-close {
    cursor: pointer;
}

.button1 {
    position: absolute;
    top: 0.5vw;
    left: 94.8vw;

}

.button2 {
    left: 2676%;
    top: 40px;

}

.button3 {
    left: 1618px;
    top: 60px
}

#imgtu1 {
    position: absolute;
    width: 2.5vw;
    height: 2.5vw;
    z-index: 80;
}

#imgtu2 {
    position: absolute;
    width: 2.5vw;
    height: 2.5vw;
    display: none;
    z-index: 80;
}

#imgtu3 {
    position: absolute;
    width: 2.5vw;
    height: 2.5vw;
    left: 16.5vw;
    z-index: 80;
}

#imgtu4 {
    position: absolute;
    width: 2.5vw;
    height: 2.5vw;
    left: 16.5vw;
    display: none;
    z-index: 80;
}

* {
    margin: 0;
    padding: 0;
    list-style-type: none;
}

/* suspend */
.suspend {
    width: 2.2vw;
    height: 50vw;
    position: fixed;
    top: 10%;
    right: 0;
    overflow: hidden;
    z-index: 9999;
}

.suspend dl {
    width: 23vw;
    height: 50vw;
    padding-left: 2.1vw;
}

.suspend dl dt {
    width: 2.2vw;
    height: 50vw;
    position: absolute;
    top: 0;
    left: 0;
    cursor: pointer;
}

.suspend dl dd.suspendQQ {
    width: 19.5vw;
    /* width:21vw; */
    height: 50vw;
    /* height:47.5vw;  */
    display: block;
    overflow: hidden;
}

.showchange1 {
    display: none;
}

.showchange2 {
    display: block;
}

/* Ã¥Å½Â»Ã©â„¢Â¤Ã©Â»ËœÃ¨Â®Â¤Ã¦Â Â·Ã¥Â¼Â */
input[type=radio] {
    margin: 0.45vw 0 0;
}

input[type=range] {
    -webkit-appearance: none;
    height: 0.6vw;
    width: 16.5vw;
    border: radius 0.5vw;; /*Ã¨Â¿â„¢Ã¤Â¸ÂªÃ¥Â±Å¾Ã¦â‚¬Â§Ã¨Â®Â¾Ã§Â½Â®Ã¤Â½Â¿Ã¥Â¡Â«Ã¥â€¦â€¦Ã¨Â¿â€ºÃ¥ÂºÂ¦Ã¦ÂÂ¡Ã¦â€”Â¶Ã§Å¡â€žÃ¥â€ºÂ¾Ã¥Â½Â¢Ã¤Â¸ÂºÃ¥Å“â€ Ã¨Â§â€™*/
}

input[type=range]::-webkit-slider-thumb {
    -webkit-appearance: none;
}

/*Ã¨Â½Â¨Ã©Ââ€œÃ¦Â·Â»Ã¥Å Â Ã¦Â Â·Ã¥Â¼Â  */
input[type=range]::-webkit-slider-runnable-track {
    /* background-color: #286090; */
    border-color: #0CBFC2;
    height: 0.7vw;
    border-radius: 10px; /*Ã¥Â°â€ Ã¨Â½Â¨Ã©Ââ€œÃ¨Â®Â¾Ã¤Â¸ÂºÃ¥Å“â€ Ã¨Â§â€™Ã§Å¡â€ž*/
    box-shadow: 0 0.01vw 0.01vw #def3f8, inset 0 .125em .125em #0d1112; /*Ã¨Â½Â¨Ã©Ââ€œÃ¥â€ â€¦Ã§Â½Â®Ã©ËœÂ´Ã¥Â½Â±Ã¦â€¢Ë†Ã¦Å¾Å“*/
}

/* Ã¥Å½Å¸Ã¥Â§â€¹Ã§Å¡â€žÃ¦Å½Â§Ã¤Â»Â¶Ã¨Å½Â·Ã¥Ââ€“Ã¥Ë†Â°Ã§â€žÂ¦Ã§â€šÂ¹Ã¦â€”Â¶Ã¯Â¼Å’Ã¤Â¼Å¡Ã¦ËœÂ¾Ã§Â¤ÂºÃ¥Å’â€¦Ã¨Â£Â¹Ã¦â€¢Â´Ã¤Â¸ÂªÃ¦Å½Â§Ã¤Â»Â¶Ã§Å¡â€žÃ¨Â¾Â¹Ã¦Â¡â€ Ã¯Â¼Å’Ã¦â€°â‚¬Ã¤Â»Â¥Ã¨Â¿ËœÃ©Å“â‚¬Ã¨Â¦ÂÃ¦Å Å Ã¨Â¾Â¹Ã¦Â¡â€ Ã¥Ââ€“Ã¦Â¶Ë†Ã£â‚¬â€š */

input[type=range]:focus {
    outline: none;
}

/* Ã§Â»â„¢Ã¦Â»â€˜Ã¥Ââ€”(thumb)Ã¦Â·Â»Ã¥Å Â Ã¦Â Â·Ã¥Â¼Â */

input[type=range]::-webkit-slider-thumb {
    -webkit-appearance: none;
    height: 1.5vw;
    width: 1.5vw;
    /* color: #0d1112;
    background-color: #209c96;
    border-color: #17948e; */
    margin-top: -0.3vw; /*Ã¤Â½Â¿Ã¦Â»â€˜Ã¥Ââ€”Ã¨Â¶â€¦Ã¥â€¡ÂºÃ¨Â½Â¨Ã©Ââ€œÃ©Æ’Â¨Ã¥Ë†â€ Ã§Å¡â€žÃ¥ÂÂÃ§Â§Â»Ã©â€¡ÂÃ§â€ºÂ¸Ã§Â­â€°*/
    background: #ffffff;
    border-radius: 50%; /*Ã¥Â¤â€“Ã¨Â§â€šÃ¨Â®Â¾Ã§Â½Â®Ã¤Â¸ÂºÃ¥Å“â€ Ã¥Â½Â¢*/
    border: solid 0.125em rgba(205, 224, 230, 0.5); /*Ã¨Â®Â¾Ã§Â½Â®Ã¨Â¾Â¹Ã¦Â¡â€ */
    box-shadow: 0 .125em .125em #3b4547; /*Ã¦Â·Â»Ã¥Å Â Ã¥Âºâ€¢Ã©Æ’Â¨Ã©ËœÂ´Ã¥Â½Â±*/
}
#tflist>div>table tbody td {
    text-align: center;
    height: 25px;
    line-height: 17px;
    border-right: 1px #dfdfdf solid;
}
* {
    margin: 0;
    padding: 0;
    font-size: 12px;
    outline: 0;
    outline-style: none;
    -moz-outline-style: none
}
ul {
    list-style: none
}

html {
    overflow: hidden
}

body {
    padding: 0;
    margin: 0;
    height: 100%;
    overflow: hidden
}

#header {
    z-index: 900;
    position: fixed
}

.top {
    height: 35px;
    line-height: 35px;
    width: 100%;
    background: #09c url("../../img/typhoon/top_bg.png") no-repeat
}

.top-r {
    float: right;
    color: #fff
}

.top-ico {
    height: 35px;
    line-height: 35px;
    cursor: pointer;
    float: left;
    padding: 0 2px
}

.top-ico:hover {
    background: #0076a9
}

.top-ico img {
    margin: 10px;
    margin-right: 5px;
    float: left
}

.user {
    float: left;
    margin: 2px 10px 0 15px;
    cursor: pointer
}

.top-bottom {
    height: 21px;
    line-height: 21px;
    background: url("/images/top_bottom.png");
    color: #333;
    font-weight: 700;
    overflow: hidden
}

.top-bottom span {
    color: #c00
}

#map {
    position: absolute;
    bottom: 0;
    width: 100%
}

.lishi1 {
    height: 172px;
    width: 100%;
    position: fixed;
    bottom: -118px;
    z-index: 906;
    /*display: none*/
}

.lishi-top1 {
    width: 165px;
    height: 52px;
    padding: 2px 2px 0 2px;
    background: #09c;
    border-radius: 4px 4px 0 0;
    margin-left: 10px;
    font-size: 12px;
    color: #fff;
    cursor: pointer;
    float: left
}

.lishi-top {
    font-size: 14px;
    height: 20px;
    line-height: 20px;
    padding-left: 0px;
    font-weight: bold;
    color: #09c;
    font-family: 'ï¿½ï¿½ï¿½ï¿½'
}

.lishi-top span {
    float:left;
    margin-right: 5px
}

.lishi-top1 input {
    width: 100%;
    width: 155px;
    height: 29px;
    line-height: 29px;
    background: url("../../img/typhoon/input_bg.png") no-repeat;
    border: 0;
    padding-left: 22px;
    margin-left: 3px;
    border-radius: 0 4px 4px 0
}

.lishi-main {
    clear: both;
    height: 123px;
    width: 100%;
    background: #09c;
    overflow: hidden
}

#lishi-b {
    border-radius: 3px
}

#lishi-b > img {
    float: left
}

.LiShi {
    background: url("../../img/typhoon/lishi_bg.png") repeat-x;
    height: 95px;
    float: left;
    overflow: visible
}

.search-Bar {
    float: left;
    height: 30px;
    line-height: 30px;
    margin-left: 3px;
    overflow: hidden;
    padding-right: 5px
}

.search-Bar input {
    padding-left: 5px;
    background: rgba(255, 255, 255, 0.7) url("/images/search_input.png") no-repeat;
    border-radius: 4px;
    width: 138px;
    height: 30px;
    border: 0;
    float: left;
    color: #666
}

.search-Bar img {
    float: left;
    position: relative;
    left: -3px
}

tspan {
    font-size: 12px
}

.lastyear, .nextyear {
    bottom: 67px;
    line-height: 55px;
    color: #fff;
    cursor: pointer;
    height: 55px;
    width: 23px;
    position: absolute;
    top: 30px;
    background: #1a9ecc
}

.lastyear {
    left: 0;
    z-index: 500
}

.nextyear {
    right: -2px;
    z-index: 500
}

.lastyear div {
    float: right;
    margin-top: 7.5px;
    margin-left: 4px;
    width: 1em;
    font-size: 12px;
    color: #fff;
    line-height: 10px;
    font-weight: bold;
    word-wrap: break-word;
    letter-spacing: 18px
}

.nextyear div {
    margin-top: 7.5px;
    float: left;
    width: 1em;
    line-height: 10px;
    font-size: 12px;
    margin-left: 3px;
    color: #fff;
    font-weight: bold;
    word-wrap: break-word;
    letter-spacing: 18px
}

.box {
    width: 289px;
    height: 365px;
    background-color: #efefef;
    position: relative;
    float: right;
    top: 56px;
    z-index: 10000
}

.box-title {
    font-size: 14px;
    color: #fff;
    font-weight: bold;
    height: 15px;
    line-height: 15px;
    z-index: 10009
}

#closeLegend {
    float: right;
    margin-right: 5px;
    margin-top: 5px;
    _margin-top: 3px;
    *margin-top: 3px;
    z-index: 1000;
    position: relative;
    cursor: pointer
}

#closebox {
    position: absolute;
    top: 56px;
    right: 285px;
    box-shadow: -2px 2px 1px #777;
    background: none repeat scroll 0 0 #777;
    z-index: 10001;
    cursor: pointer
}

.tfdq img {
    float: left
}

.tfdq {
    height: 26px;
    background: #09c;
    width: 100%;
    margin: auto;
    color: #fff;
    line-height: 26px
}

#tflist > table {
    width: 269px;
    margin: auto;
    margin-top: 10px;
    background: white
}

#tflist > div > table {
    width: 100%;
    margin: auto;
    font-family: 'ï¿½ï¿½ï¿½ï¿½';
    font-size: 13px;
    color: #666;
    background: white
}

#tflist > table thead td {
    background: #eee;
    color: #666;
    text-align: center;
    height: 25px;
    line-height: 25px;
    font: bold 13px 'ï¿½ï¿½ï¿½ï¿½';
    border-collapse: collapse
}



#tflist > div > table tbody .white td {
    line-height: 25px
}

.ul {
    overflow: hidden;
    margin-left: 7px;
    margin-top: 5px
}

.ul img {
    float: left;
    margin: 0
}

.xtb {
    position: absolute;
    top: 56px;
    cursor: pointer;
    width: 35px;
    height: 36px;
    z-index: 10006
}

#x {
    cursor: pointer
}

#back {
    float: left;
    margin-top: 3px;
    margin-left: 10px;
    margin-right: 19px
}

.tooltip {
    display: none;
    position: absolute;
    top: 300px;
    left: 50%;
    text-align: center;
    z-index: 18901
}

.tooltip-box {
    overflow: hidden
}

.tooltip-body {
    float: left;
    background: url("/images/tooltip/bg.png") repeat-x;
    min-width: 90px;
    height: 48px;
    padding: 6px 5px;
    color: #fff;
    text-align: center;
    font-size: 12px
}

.tooltip-b {
    position: relative;
    bottom: 4px
}

#CloudRainLegend {
    position: fixed;
    top: 61px;
    height: 41px;
    font-weight: bold;
    display: none;
    width: auto
}

.stip {
    float: left;
    width: auto;
    height: 41px;
    border: 2px #cbdfe2 solid;
    font-weight: bold;
    background: #fff;
    padding: 1px;
    padding: 0 6px 0 15px
}

.stip i {
    font-size: 20px;
    font-weight: bold;
    line-height: 16px;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    display: block;
    float: right;
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    color: #fe3f3f;
    padding-right: 3px;
    cursor: pointer
}

.stip span {
    font-weight: 200;
    padding-right: 15px
}

#mapTypeSelect img {
    border: 0;
    cursor: pointer;
    z-index: 10009
}

#BoxControlButton img {
    border: 0;
    cursor: pointer
}

.td125 {
    width: 105px
}

.td35 {
    width: 114px
}

.td25 {
    width: 45px
}
tbody tr:hover{
    cursor:pointer;
    background-color: rgba(35,198,200,0.3) !important;
}
.typhoonNameLabel {
    color: #ec2828;
    font: bold 22px/1.5 "Helvetica Neue", Arial, Helvetica, sans-serif;
    letter-spacing: 3px;
    position: absolute;
    text-align: center;
    text-shadow: 1px 0 0 #fff, 0 1px 0 #fff, -1px 0 0 #fff, 0 -1px 0 #fff;
    top: 65px;
    white-space: nowrap;
    width: 60%;
    z-index: 2009;
    margin-left: 20%
}

.popupinfo {
    width: 100%;
    font: 13px 'ï¿½ï¿½ï¿½ï¿½';
    color: #333;
    letter-spacing: 3px;
    opacity: .8
}

.popupinfo td {
    height: 23px;
    line-height: 23px
}

.popupinfotd1 {
    width: 75px
}

.tdhnormal {
    background-color: white !important
}

.tdeven {
    background-color: #f1f2f4 !important
}

.tdhover {
    background-color: yellow !important
}

.tdclick {
    background-color: #ffdb00 !important
}

#typhoonlegend {
    position: relative;
    float: left;
    padding-left: 5px;
    display: none
}

.leaflet-div-land {
    height: 10px;
    white-space: nowrap;
    font: 12px/1.5 "Helvetica Neue", Arial, Helvetica, sans-serif;
    color: #00f;
    text-shadow: #fff 1px 0 0, #fff 0 1px 0, #fff -1px 0 0, #fff 0 -1px 0
}

.leaflet-divicon {
    height: 8px;
    white-space: nowrap;
    font: 9px/0.9 'ï¿½ï¿½ï¿½ï¿½', Arial, Helvetica, sans-serif;
    color: #333;
    text-shadow: #edeaea 1px 0 0, #edeaea 0 1px 0, #edeaea -1px 0 0, #edeaea 0 -1px 0
}

#set {
    width: 100%;
    position: absolute;
    background: #fff;
    height: 400px;
    z-index: 10010;
    display: none
}

#me_gutter {
    width: 120px;
    height: 100%;
    position: absolute;
    background: #09c;
    padding-top: 47px
}

.me_backarrow {
    margin-left: 40px;
    background: url("/images/back1.png") no-repeat transparent;
    display: block;
    height: 40px;
    width: 40px;
    cursor: pointer
}

.me_feature {
    display: block;
    cursor: pointer;
    width: 120px;
    margin-top: 80px;
    position: relative
}

.me_feature_icon_settings {
    margin-left: 40px;
    background: url("/images/setting.png") no-repeat transparent;
    display: block;
    height: 40px;
    width: 40px;
    cursor: pointer
}

.me_feature_icon_logout {
    margin-left: 40px;
    margin-top: 40px;
    background: url("/images/logout.png") no-repeat transparent;
    display: block;
    height: 40px;
    width: 40px;
    cursor: pointer
}

.me_feature_title {
    display: block;
    margin-top: 10px;
    font-size: 12px;
    text-align: center;
    cursor: pointer;
    color: #efefef
}

.me_feature_caret {
    border-color: transparent #fff transparent transparent;
    border-width: 11px;
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    right: 0;
    border-style: solid;
    top: 12px
}

#me_wrapper {
    margin: 35px 50px 0 180px
}

.me_header {
    border-bottom: 1px #a6a6a6 solid;
    padding-bottom: 20px;
    color: #333;
    font-size: 14px
}

.me_nav li {
    padding: 5px 2px;
    float: left;
    font-size: 14px;
    font-family: "SimSun";
    color: #666;
    font-weight: 600;
    margin-right: 25px
}

.me_nav .cur {
    border-top: 3px #a6a6a6 solid;
    color: #363636
}

.me_ty {
    clear: both;
    width: 450px
}

.me_ty td {
    height: 80px;
    font-size: 13px
}

.me_ty td input[type='checkbox'] {
    margin-right: 10px
}

.me_ty td span {
    font-weight: 600;
    margin-right: 10px
}

.me_ty td select {
    width: 125px;
    border: 1px #a6a6a6 solid;
    height: 22px
}

.btn_blue {
    width: 85px;
    height: 30px;
    background: #09c;
    border: 0;
    color: #fff;
    text-align: center;
    line-height: 30px;
    display: block;
    text-decoration: none
}

.search_view {
    padding: 8px;
    position: absolute;
    z-index: 10000;
    background: #fff;
    border: 1px #ccc solid;
    display: none
}

.search_view input[type='text'] {
    height: 25px;
    width: 270px;
    border: 1px #ccc solid;
    padding-left: 5px
}

.search_view img {
    position: absolute;
    right: 20px;
    top: 16px
}

.search_view .dropdown-menu li a {
    line-height: 25px;
    padding-left: 5px;
    text-decoration: none;
    color: #333;
    font-size: 13px
}

.tfinfo {
    width: 250px;
    background: #fff;
    z-index: 10009
}

.tfinfo li {
    background: #fff;
    border-width: 0;
    margin: 0 13px;
    color: #a3a3a3;
    height: 25px;
    line-height: 25px;
    border-bottom: 1px #ddd solid
}

.tfinfo li.multiline {
    height: auto;
    min-height: 25px;
    overflow: hidden
}

.tfinfo li.multiline span {
    display: block;
    width: 155px;
    line-height: 16px;
    float: right;
    padding-top: 2px
}

.tfinfo li:last-child {
    background: #fff;
    border-bottom: 0
}

.tfinfo li > span {
    margin-left: 17px;
    color: #666
}

.tfinfo .tfinfo-head {
    padding-left: 13px;
    margin: 0;
    height: 35px;
    line-height: 35px;
    border: 1px #aeb1b7 solid;
    border-bottom: 0;
    background: #09c;
    color: #fff;
    padding-left: 1px
}

.tfinfo .tfinfo-head span {
    color: #fff
}

.tfinfo .tfinfo-foot {
    background: 0;
    border: 0
}

.tfinfo .tfinfo-foot img {
    float: left
}

.tfinfo1 {
    width: 180px;
    z-index: 10009
}

.tfinfo1 li {
    border-width: 0;
    padding-left: 17px;
    color: #a3a3a3;
    line-height: 30px;
    word-wrap: break-word
}

.tfinfo1 li a {
    text-decoration: none;
    color: #a3a3a3;
    font: normal 12px Arial
}

.tfinfo1 li a:hover {
    text-decoration: none;
    color: #ff6201
}

.tfinfo1 .tfinfo-head {
    height: 35px;
    line-height: 35px;
    border: 1px #aeb1b7 solid;
    border-bottom: 0;
    background: #09c;
    color: #fff;
    padding-left: 1px
}

.search-bar {
    z-index: 10009;
    height: 40px;
    min-width: 64px;
    background-color: #0099c8;
    color: #666;
    line-height: 40px;
    cursor: pointer
}

.search-bar_left {
    position: relative;
    float: left;
    right: 10px
}

.search-bar_right {
    position: relative;
    float: right;
    left: 10px
}

.ico_search {
    float: left;
    margin-top: 5px;
    position: relative;
    right: 10px
}

.search-bar-1 {
    position: absolute;
    overflow: hidden;
    height: 25px;
    top: 0;
    left: 0;
    z-index: 100;
    width: 0
}

.search-bar-1 input {
    margin: 2px 10px 3px 0;
    height: 21px;
    width: 140px;
    float: left;
    padding-left: 3px;
    border-radius: 2px;
    background-color: #FFF;
    border: 1px solid #CCC;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
    transition: border .2s linear 0s, box-shadow .2s linear 0s
}

.search-bar-panel {
    position: absolute;
    min-width: 200px;
    left: 39px;
    bottom: 151px;
    z-index: 10006
}

.search-bar-panel ul {
    padding: 3px 0;
    display: block;
    border-radius: 6px;
    list-style: none outside none;
    background-color: #FFF;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    background-clip: padding-box;
    color: #333;
    margin: 0
}

.search-bar-panel ul li {
    line-height: 20px;
    list-style: none outside none
}

.search-bar-panel ul li a {
    font-size: 13px;
    display: block;
    padding: 2px 15px 2px 10px;
    clear: both;
    font-weight: normal;
    line-height: 20px;
    color: #333;
    white-space: nowrap;
    text-decoration: none
}

.search-bar-panel ul li a:hover {
    color: #FFF;
    background-color: #0081c2;
    background-image: linear-gradient(to bottom, #08C, #0077b3);
    background-repeat: repeat-x
}

.divider {
    height: 1px;
    overflow: hidden;
    background-color: #e5e5e5
}

#btn_search {
    position: absolute;
    left: 110px
}

.DistanceLabelStyle .bubbleLabel {
    background-color: white !important;
    border: 1px solid black !important;
    color: black !important;
    font-weight: normal !important;
    white-space: nowrap;
    position: absolute
}

.DistanceLabelStyle .bubbleLabel span.bubbleLabel-bot-left {
    border-color: transparent black transparent transparent !important
}

.DistanceLabelStyle .bubbleLabel span.bubbleLabel-top-left {
    border-color: transparent white transparent transparent !important
}

.bubbleLabel {
    border: 1px solid #beceeb;
    border-radius: 3px;
    display: inline-block;
    padding: 2px 5px;
    position: relative;
    text-align: center
}

.bubbleLabel .bubbleLabel-bot, .bubbleLabel .bubbleLabel-top {
    border-width: 8px;
    font-size: 0;
    height: 0;
    overflow: hidden;
    position: absolute;
    width: 0
}

.bubbleLabel span.bubbleLabel-bot-left {
    border-color: rgba(0, 0, 0, 0) #beceeb rgba(0, 0, 0, 0) rgba(0, 0, 0, 0);
    border-style: dashed solid dashed dashed;
    left: -15px;
    top: 3px
}

.bubbleLabel span.bubbleLabel-top-left {
    border-color: rgba(0, 0, 0, 0) #ffa500 rgba(0, 0, 0, 0) rgba(0, 0, 0, 0);
    border-style: dashed solid dashed dashed;
    left: -14px;
    top: 3px
}

.panel {
    width: 300px;
    min-height: 68px;
    background: url("/images/panel/panel.png") no-repeat;
    margin-right: 5px
}

.panel .closeImg {
    float: right;
    position: relative;
    left: 6px;
    bottom: 3px;
    cursor: pointer
}

.panel-head {
    width: 288px;
    margin: auto;
    height: 31px;
    line-height: 24px;
    border-bottom: 1px #dfdfdf solid;
    padding: 6px 0 0 8px
}

.panel-head span {
    margin-top: 3px;
    color: #333;
    font-family: 'Î¢ï¿½ï¿½ï¿½Åºï¿½'
}

.panel-head img {
    float: right;
    margin-right: 10px;
    cursor: pointer
}

.panel ul {
    width: 288px;
    margin: auto;
    margin-top: 10px;
    text-align: center
}

.panel ul li {
    margin: 0 5px;
    list-style-type: none;
    float: left;
    text-align: center
}

.panel ul li span {
    color: #333;
    display: block
}

#legend_img {
    -webkit-appearance: none;
    left: 10px;
    top: 174px;
    position: relative;
    z-index: 1000;
    cursor: pointer;
    width: 40px
}

#legend {
    left: 10px;
    bottom: 10px;
    position: absolute;
    z-index: 1000;
    background-color: #fff
}

#legend .table td {
    text-align: center;
    width: 48px;
    padding: 0 6px;
    border-left: 1px solid #ccc
}

#legend .table th {
    padding: 0 4px;
    color: #fff;
    background-color: #ff4500
}

#legend table td {
    text-align: center;
    width: 48px;
    padding: 0 6px
}

#legend table th {
    padding: 0 4px;
    color: #fff;
    background-color: #ff4500
}

.modal2 {
    width: 340px;
    height: 418px;
    display: none;
    border: 5px solid #f2f3f5;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px;
    -khtml-border-radius: 5px;
    background: #f2f3f5
}

#loginmodal .close {
    float: right;
    font-size: 20px;
    font-weight: bold;
    line-height: 20px;
    color: #000;
    text-shadow: 0 1px 0 #FFF;
    opacity: .2;
    padding: 0;
    cursor: pointer;
    background: none repeat scroll 0 0 transparent;
    border: 0 none
}

#loginmodal h2 {
    font-size: 48px;
    color: #343438;
    text-align: center;
    margin: 30px 0 13px 0
}

#loginmodal p {
    width: 300px;
    margin: auto;
    margin-top: 15px
}

#loginmodal p label {
    color: #9ca1a7;
    line-height: 22px;
    height: 22px
}

#loginmodal input[type='text'], #loginmodal input[type='password'] {
    padding: 10px;
    width: 100%;
    border: 2px solid #cfd0d2;
    background: none repeat scroll 0 0 transparent;
    color: #716f6d;
    font-weight: 300;
    font-size: 2.2em;
    line-height: normal
}

#loginmodal p button {
    display: block;
    padding: 1.5em;
    width: 100%;
    border: medium none;
    background: none repeat scroll 0 0 #1188c0;
    color: #f9f6e5;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 800;
    font-size: 1.25em;
    margin-top: 2.5em;
    cursor: pointer
}

#load {
    background: #09c no-repeat center center;
    width: 100%;
    position: absolute;
    z-index: 20000;
    text-align: center
}

.leaflet-control-container > .leaflet-left > .leaflet-control-zoom {
    margin-top: 44px
}

.leaflet-control-container > .leaflet-left > .leaflet-control-zoom > a {
    width: 26px;
    height: 26px;
    line-height: 26px
}

.leaflet-popup > .leaflet-popup-content-wrapper {
    border-radius: 0
}

.leaflet-popup > .leaflet-popup-content-wrapper .leaflet-popup-content {
    margin: 0
}

.windCircle > .leaflet-popup-content-wrapper {
    padding: 3px
}

@import url("/styles/font-awesome.css");
*, *:after, *:before {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -o-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0;
    margin: 0
}

.clearfix:after {
    content: "";
    display: table;
    clear: both
}

.form-1 {
    width: 300px;
    margin: 20px auto 30px;
    padding: 10px;
    position: relative;
    box-shadow: 0 0 1px rgba(0, 0, 0, 0.3), 0 3px 7px rgba(0, 0, 0, 0.3), inset 0 1px rgba(255, 255, 255, 1), inset 0 -3px 2px rgba(0, 0, 0, 0.25);
    border-radius: 5px;
    background: white;
    background: -moz-linear-gradient(#eeefef, #fff 10%);
    background: -ms-linear-gradient(#eeefef, #fff 10%);
    background: -o-linear-gradient(#eeefef, #fff 10%);
    background: -webkit-gradient(linear, 0 0, 0 100%, from(#eeefef), color-stop(0.1, #fff));
    background: -webkit-linear-gradient(#eeefef, #fff 10%);
    background: linear-gradient(#eeefef, #fff 10%)
}

.form-1 .field {
    position: relative
}

.form-1 .field i {
    left: 0;
    top: 0;
    position: absolute;
    height: 36px;
    width: 36px;
    border-right: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 1px 0 0 rgba(255, 255, 255, 0.7);
    color: #777;
    text-align: center;
    line-height: 42px;
    -webkit-transition: all .3s ease-out;
    -moz-transition: all .3s ease-out;
    -ms-transition: all .3s ease-out;
    -o-transition: all .3s ease-out;
    transition: all .3s ease-out;
    pointer-events: none
}

.form-1 input[type=text], .form-1 input[type=password] {
    font-family: Arial, sans-serif;
    font-size: 13px;
    font-weight: 400;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8);
    width: 100%;
    padding: 10px 18px 10px 45px;
    border: 0;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1), inset 0 3px 2px rgba(0, 0, 0, 0.1);
    border-radius: 3px;
    background: #f9f9f9;
    color: #777;
    -webkit-transition: color .3s ease-out;
    -moz-transition: color .3s ease-out;
    -ms-transition: color .3s ease-out;
    -o-transition: color .3s ease-out;
    transition: color .3s ease-out
}

.form-1 input[type=text] {
    margin-bottom: 10px
}

.form-1 input[type=text]:hover ~ i, .form-1 input[type=password]:hover ~ i {
    color: #52cfeb
}

.form-1 input[type=text]:focus ~ i, .form-1 input[type=password]:focus ~ i {
    color: #42a2bc
}

.form-1 input[type=text]:focus, .form-1 input[type=password]:focus, .form-1 button[type=submit]:focus {
    outline: 0
}

.form-1 .submit {
    width: 65px;
    height: 65px;
    position: absolute;
    top: 17px;
    right: -25px;
    padding: 10px;
    z-index: 2;
    background: #fff;
    border-radius: 50%;
    box-shadow: 0 0 2px rgba(0, 0, 0, 0.1), 0 3px 2px rgba(0, 0, 0, 0.1), inset 0 -3px 2px rgba(0, 0, 0, 0.2)
}

.form-1 .submit:after {
    content: "";
    width: 10px;
    height: 10px;
    position: absolute;
    top: -2px;
    left: 30px;
    background: #fff;
    box-shadow: 0 62px white, -32px 31px white
}

.form-1 button {
    width: 100%;
    height: 100%;
    margin-top: -1px;
    font-size: 1.4em;
    line-height: 1.75;
    color: white;
    border: 0;
    border-radius: inherit;
    background: #52cfeb;
    background: -moz-linear-gradient(#52cfeb, #42a2bc);
    background: -ms-linear-gradient(#52cfeb, #42a2bc);
    background: -o-linear-gradient(#52cfeb, #42a2bc);
    background: -webkit-gradient(linear, 0 0, 0 100%, from(#52cfeb), to(#42a2bc));
    background: -webkit-linear-gradient(#52cfeb, #42a2bc);
    background: linear-gradient(#52cfeb, #42a2bc);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.3), 0 1px 2px rgba(0, 0, 0, 0.35), inset 0 3px 2px rgba(255, 255, 255, 0.2), inset 0 -3px 2px rgba(0, 0, 0, 0.1);
    cursor: pointer
}

.form-1 button:hover, .form-1 button[type=submit]:focus {
    background: #52cfeb;
    -webkit-transition: all .3s ease-out;
    -moz-transition: all .3s ease-out;
    -ms-transition: all .3s ease-out;
    -o-transition: all .3s ease-out;
    transition: all .3s ease-out
}

.form-1 button:active {
    background: #42a2bc;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3), inset 0 3px 4px rgba(0, 0, 0, 0.3)
}

.tf-box {
    width: 285px;
    position: absolute;
    right: 0;
    top: 56px;
    z-index: 10000;
    background: #09c;
    box-shadow: -2px 2px 1px #777;
    overflow: visible;
    padding-bottom: 5px
}

.tf-box .search-inputDiv {
    height: 30px;
    padding: 9px 0 5px 10px
}

.tf-box .search-inputDiv > input {
    width: 230px;
    height: 25px;
    line-height: 25px;
    padding-left: 10px;
    border-radius: 10px;
    border: 0;
    float: left
}

.tf-box .search-inputDiv > img {
    float: right;
    position: absolute;
    right: 44px;
    top: 12px
}

.tf-box .search-inputDiv > span {
    color: #fff;
    float: right;
    margin-right: 10px;
    margin-top: 3px
}

.tf-box .tf-list {
    background: #FFF;
    width: 279px;
    background: #fff;
    margin: 0 auto 0 auto;
    padding-top: 4px
}

#CameraList {
    height: 180px;
    overflow: auto;
    display: none;
    background: #fff;
    margin-left: 3px;
    width: 279px
}

#CameraList li {
    width: 262px;
    height: 25px;
    padding-left: 14px;
    line-height: 25px;
    font-size: 12px;
    height: 34px;
    color: #333;
    border-bottom: 1px #efefef solid;
    cursor: pointer
}

#CameraList li:hover {
    background: #efefef
}

#CameraList li > img {
    margin-right: 10px;
    vertical-align: -10px
}

.tf-list-head {
    width: 275px;
    margin: auto;
    line-height: 20px;
    height: 38px;
    padding-left: 10px;
    padding-top: 8px;
    border-bottom: 1px #e6e6e6 solid;
    background: #f3f3f3
}

.tf-list-head > span {
    margin-left: 12px;
    color: #333;
    margin-right: 40px;
    font-size: 13px;
    font-weight: bold
}

.tf-list-head .tf-list-head-slider {
    width: 0;
    overflow: hidden;
    height: 38px;
    background: #e36926;
    position: absolute;
    right: 4px;
    margin-top: -29px
}

.tf-list-head .tf-list-head-slider > img {
    float: right;
    margin: 7px 4px 0 4px;
    cursor: pointer
}

.tf-box .tf-list .tfbtn-name {
    width: 65px;
    height: 20px;
    text-align: center;
    line-height: 18px;
    float: left;
    margin-left: 10px
}

.tf-box .tf-list .tfbtn-name > img {
    float: right;
    margin-right: 5px;
    margin-top: 4px
}

.tfnameselect {
    color: #fff;
    cursor: pointer;
    background: #0098ca;
    border: 1px #017ea9 solid
}

.tfnameunselect {
    color: #333;
    cursor: pointer;
    background: #fff;
    border: 1px #e2e2e2 solid
}

.tf-list > ul {
    width: 274px;
    height: 80px;
    overflow: hidden
}

.tf-list > ul > li {
    width: 91.3px;
    height: 40px;
    float: left
}

.tf-list > ul > li > a {
    width: 70px;
    height: 23px;
    display: block;
    border-radius: 3px;
    border: 1px #ddd solid;
    background: #fff;
    margin: auto;
    margin-top: 6px;
    text-align: center;
    line-height: 23px;
    text-decoration: none;
    color: #666
}

.tf-list > ul > li > a > span {
    width: 25px;
    height: 14px;
    color: #fff;
    background: red;
    text-align: center;
    line-height: 14px;
    position: absolute;
    border-radius: 5px;
    margin-top: -6px
}

.tf-list h4 {
    float: left;
    width: 68.5px;
    text-align: center;
    margin-top: 7px;
    font-size: 15px;
    color: #666;
    line-height: 22px;
    font-weight: normal
}

.tf-box .slider {
    width: 237px;
    height: 14px;
    line-height: 14px;
    margin-bottom: -10px;
    margin: 5px;
    position: absolute;
    background: url("/images/tfbox/bar.png") no-repeat left center
}

.tf-box .slider-blue {
    width: 60px;
    background: url("/images/tfbox/bar_blue.png") no-repeat left center
}

.namelist {
    height: 45px;
    margin-top: 10px
}

.namelist li {
    float: left;
    height: 43px;
    width: 64px;
    line-height: 18px;
    padding: 3px 6px 5px 5px;
    margin-left: 3px;
    margin-right: 3px;
    background: #eee;
    color: #666;
    border-radius: 4px;
    cursor: pointer
}
.namelist li a{
    z-index:9998!important;
    position: absolute;
    margin-left: 48px;
    top: 8px;
    width: 14px!important;

}


.namelist li i {
    float: right;
    height: 14px;
    width: 14px;
    position: relative;
    left: 13px;
    bottom: 9px;
    cursor: pointer
}

.namelist li .namelist-refresh {
    background: url("/images/tfbox/refreshn.png") no-repeat center center
}

.namelist li .namelist-close {
    background: url("/images/tfbox/closen.png") no-repeat center center
}

.namelist .namelist-select {
    height: 43px;
    background: #fff;
    color: #999;
    border-radius: 4px 4px 0 0
}

.namelist .namelist-select span {
    color: #1188c0
}

.tuli {
    width: 190px;
    position: fixed;
    z-index: 10005;
    left: 46px;
    top: 110px;
    box-shadow: 1.5px 1.5px 2px #777;
    display: none
}

.tuli .tuli-head {
    height: 26px;
    background-color: #39aed5;
    line-height: 26px;
    color: #fff;
    font-size: 13.5px;
    font-weight: bold;
    padding-left: 5px
}

.tuli .tuli-li {
    border-bottom: 1px #dee0e1 solid;
    background: #fff;
    padding: 0 10px;
    line-height: 30px;
    height: 30px
}

.tuli .tuli-li > span {
    float: left;
    font-size: 13px;
    font-weight: bold
}

.tuli-li .icheckbox_flat-blue {
    float: right;
    margin-top: 5px
}

.tuli > ul {
    height: 70px;
    overflow: hidden;
    background: #f0f8fe
}

.tuli > ul > li {
    float: left;
    width: 88px;
    height: 23.3px;
    line-height: 23.3px;
    color: #666;
    margin-left: 3px
}

.tuli > ul > li > img {
    margin-left: 5px
}

.lishi-top2 {
    background: url("/images/lix_bg.png") repeat-x;
    border-radius: 0 4px 4px 0;
    width: 155px;
    height: 29px;
    margin-left: 3px;
    color: #09c;
    line-height: 29px;
    display: none
}

.lishi-top2 span {
    float: left;
    margin-left: 15px;
    font-weight: bold;
    cursor: pointer
}

.leaflet-pane, .leaflet-tile, .leaflet-marker-icon, .leaflet-marker-shadow, .leaflet-tile-container, .leaflet-pane > svg, .leaflet-pane > canvas, .leaflet-zoom-box, .leaflet-image-layer, .leaflet-layer {
    position: absolute;
    left: 0;
    top: 0
}

.leaflet-container {
    overflow: hidden
}

.leaflet-tile, .leaflet-marker-icon, .leaflet-marker-shadow {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    -webkit-user-drag: none
}

.leaflet-safari .leaflet-tile {
    image-rendering: -webkit-optimize-contrast
}

.leaflet-safari .leaflet-tile-container {
    width: 1600px;
    height: 1600px;
    -webkit-transform-origin: 0 0
}

.leaflet-marker-icon, .leaflet-marker-shadow {
    display: block
}

.leaflet-container .leaflet-overlay-pane svg, .leaflet-container .leaflet-marker-pane img, .leaflet-container .leaflet-shadow-pane img, .leaflet-container .leaflet-tile-pane img, .leaflet-container img.leaflet-image-layer {
    max-width: none !important;
    max-height: none !important
}

.leaflet-container.leaflet-touch-zoom {
    -ms-touch-action: pan-x pan-y;
    touch-action: pan-x pan-y
}

.leaflet-container.leaflet-touch-drag {
    -ms-touch-action: pinch-zoom;
    touch-action: none;
    touch-action: pinch-zoom
}

.leaflet-container.leaflet-touch-drag.leaflet-touch-zoom {
    -ms-touch-action: none;
    touch-action: none
}

.leaflet-container {
    -webkit-tap-highlight-color: transparent
}

.leaflet-container a {
    -webkit-tap-highlight-color: rgba(51, 181, 229, 0.4)
}

.leaflet-tile {
    filter: inherit;
    visibility: hidden
}

.leaflet-tile-loaded {
    visibility: inherit
}

.leaflet-zoom-box {
    width: 0;
    height: 0;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    z-index: 800
}

.leaflet-overlay-pane svg {
    -moz-user-select: none
}

.leaflet-pane {
    z-index: 400
}

.leaflet-tile-pane {
    z-index: 200
}

.leaflet-overlay-pane {
    z-index: 400
}

.leaflet-shadow-pane {
    z-index: 500
}

.leaflet-marker-pane {
    z-index: 600
}

.leaflet-tooltip-pane {
    z-index: 650
}

.leaflet-popup-pane {
    z-index: 700
}

.leaflet-map-pane canvas {
    z-index: 100
}

.leaflet-map-pane svg {
    z-index: 200
}

.leaflet-vml-shape {
    width: 1px;
    height: 1px
}

.lvml {
    behavior: url("/App_Data/javascript/mapengine/#default#VML");
    display: inline-block;
    position: absolute
}

.leaflet-control {
    position: relative;
    z-index: 800;
    pointer-events: visiblePainted;
    pointer-events: auto
}

.leaflet-top, .leaflet-bottom {
    position: absolute;
    z-index: 1000;
    pointer-events: none
}

.leaflet-top {
    top: 0
}

.leaflet-right {
    right: 0
}

.leaflet-bottom {
    bottom: 0
}

.leaflet-left {
    left: 0
}

.leaflet-control {
    float: left;
    clear: both
}

.leaflet-right .leaflet-control {
    float: right
}

.leaflet-top .leaflet-control {
    margin-top: 10px
}

.leaflet-bottom .leaflet-control {
    margin-bottom: 10px
}

.leaflet-left .leaflet-control {
    margin-left: 10px
}

.leaflet-right .leaflet-control {
    margin-right: 10px
}

.leaflet-fade-anim .leaflet-tile {
    will-change: opacity
}

.leaflet-fade-anim .leaflet-popup {
    opacity: 0;
    -webkit-transition: opacity .2s linear;
    -moz-transition: opacity .2s linear;
    -o-transition: opacity .2s linear;
    transition: opacity .2s linear
}

.leaflet-fade-anim .leaflet-map-pane .leaflet-popup {
    opacity: 1
}

.leaflet-zoom-animated {
    -webkit-transform-origin: 0 0;
    -ms-transform-origin: 0 0;
    transform-origin: 0 0
}

.leaflet-zoom-anim .leaflet-zoom-animated {
    will-change: transform
}

.leaflet-zoom-anim .leaflet-zoom-animated {
    -webkit-transition: -webkit-transform .25s cubic-bezier(0, 0, 0.25, 1);
    -moz-transition: -moz-transform .25s cubic-bezier(0, 0, 0.25, 1);
    -o-transition: -o-transform .25s cubic-bezier(0, 0, 0.25, 1);
    transition: transform .25s cubic-bezier(0, 0, 0.25, 1)
}

.leaflet-zoom-anim .leaflet-tile, .leaflet-pan-anim .leaflet-tile {
    -webkit-transition: none;
    -moz-transition: none;
    -o-transition: none;
    transition: none
}

.leaflet-zoom-anim .leaflet-zoom-hide {
    visibility: hidden
}

.leaflet-interactive {
    cursor: pointer
}

.leaflet-grab {
    cursor: -webkit-grab;
    cursor: -moz-grab
}

.leaflet-crosshair, .leaflet-crosshair .leaflet-interactive {
    cursor: crosshair
}

.leaflet-popup-pane, .leaflet-control {
    cursor: auto
}

.leaflet-dragging .leaflet-grab, .leaflet-dragging .leaflet-grab .leaflet-interactive, .leaflet-dragging .leaflet-marker-draggable {
    cursor: move;
    cursor: -webkit-grabbing;
    cursor: -moz-grabbing
}

.leaflet-marker-icon, .leaflet-marker-shadow, .leaflet-image-layer, .leaflet-pane > svg path, .leaflet-tile-container {
    pointer-events: none
}

.leaflet-marker-icon.leaflet-interactive, .leaflet-image-layer.leaflet-interactive, .leaflet-pane > svg path.leaflet-interactive {
    pointer-events: visiblePainted;
    pointer-events: auto
}

.leaflet-container {
    background: #ddd;
    outline: 0
}

.leaflet-container a {
    color: #0078A8
}

.leaflet-container a.leaflet-active {
    outline: 2px solid orange
}

.leaflet-zoom-box {
    border: 2px dotted #38f;
    background: rgba(255, 255, 255, 0.5)
}

.leaflet-container {
    font: 12px/1.5 "Helvetica Neue", Arial, Helvetica, sans-serif
}

.leaflet-bar {
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.65);
    border-radius: 4px
}

.leaflet-bar a, .leaflet-bar a:hover {
    background-color: #fff;
    border-bottom: 1px solid #ccc;
    width: 26px;
    height: 26px;
    line-height: 26px;
    display: block;
    text-align: center;
    text-decoration: none;
    color: black
}

.leaflet-bar a, .leaflet-control-layers-toggle {
    background-position: 50% 50%;
    background-repeat: no-repeat;
    display: block
}

.leaflet-bar a:hover {
    background-color: #f4f4f4
}

.leaflet-bar a:first-child {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px
}

.leaflet-bar a:last-child {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    border-bottom: 0
}

.leaflet-bar a.leaflet-disabled {
    cursor: default;
    background-color: #f4f4f4;
    color: #bbb
}

.leaflet-touch .leaflet-bar a {
    width: 30px;
    height: 30px;
    line-height: 30px
}

.leaflet-touch .leaflet-bar a:first-child {
    border-top-left-radius: 2px;
    border-top-right-radius: 2px
}

.leaflet-touch .leaflet-bar a:last-child {
    border-bottom-left-radius: 2px;
    border-bottom-right-radius: 2px
}

.leaflet-control-zoom-in, .leaflet-control-zoom-out {
    font: bold 18px 'Lucida Console', Monaco, monospace;
    text-indent: 1px
}

.leaflet-touch .leaflet-control-zoom-in, .leaflet-touch .leaflet-control-zoom-out {
    font-size: 22px
}

.leaflet-control-layers {
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.4);
    background: #fff;
    border-radius: 5px
}

.leaflet-control-layers-toggle {
    background-image: url("/App_Data/javascript/mapengine/images/layers.png");
    width: 36px;
    height: 36px
}

.leaflet-retina .leaflet-control-layers-toggle {
    background-image: url("/App_Data/javascript/mapengine/images/layers-2x.png");
    background-size: 26px 26px
}

.leaflet-touch .leaflet-control-layers-toggle {
    width: 44px;
    height: 44px
}

.leaflet-control-layers .leaflet-control-layers-list, .leaflet-control-layers-expanded .leaflet-control-layers-toggle {
    display: none
}

.leaflet-control-layers-expanded .leaflet-control-layers-list {
    display: block;
    position: relative
}

.leaflet-control-layers-expanded {
    padding: 6px 10px 6px 6px;
    color: #333;
    background: #fff
}

.leaflet-control-layers-scrollbar {
    overflow-y: scroll;
    overflow-x: hidden;
    padding-right: 5px
}

.leaflet-control-layers-selector {
    margin-top: 2px;
    position: relative;
    top: 1px
}

.leaflet-control-layers label {
    display: block
}

.leaflet-control-layers-separator {
    height: 0;
    border-top: 1px solid #ddd;
    margin: 5px -10px 5px -6px
}

.leaflet-default-icon-path {
    background-image: url("/App_Data/javascript/mapengine/images/marker-icon.png")
}

.leaflet-container .leaflet-control-attribution {
    background: #fff;
    background: rgba(255, 255, 255, 0.7);
    margin: 0
}

.leaflet-control-attribution, .leaflet-control-scale-line {
    padding: 0 5px;
    color: #333
}

.leaflet-control-attribution a {
    text-decoration: none
}

.leaflet-control-attribution a:hover {
    text-decoration: underline
}

.leaflet-container .leaflet-control-attribution, .leaflet-container .leaflet-control-scale {
    font-size: 11px
}

.leaflet-left .leaflet-control-scale {
    margin-left: 5px
}

.leaflet-bottom .leaflet-control-scale {
    margin-bottom: 5px
}

.leaflet-control-scale-line {
    border: 2px solid #777;
    border-top: 0;
    line-height: 1.1;
    padding: 2px 5px 1px;
    font-size: 11px;
    white-space: nowrap;
    overflow: hidden;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    background: #fff;
    background: rgba(255, 255, 255, 0.5)
}

.leaflet-control-scale-line:not(:first-child) {
    border-top: 2px solid #777;
    border-bottom: 0;
    margin-top: -2px
}

.leaflet-control-scale-line:not(:first-child):not(:last-child) {
    border-bottom: 2px solid #777
}

.leaflet-touch .leaflet-control-attribution, .leaflet-touch .leaflet-control-layers, .leaflet-touch .leaflet-bar {
    box-shadow: none
}

.leaflet-touch .leaflet-control-layers, .leaflet-touch .leaflet-bar {
    border: 2px solid rgba(0, 0, 0, 0.2);
    background-clip: padding-box
}

.leaflet-popup {
    position: absolute;
    text-align: center;
    margin-bottom: 20px
}

.leaflet-popup-content-wrapper {
    padding: 1px;
    text-align: left;
    border-radius: 12px
}

.leaflet-popup-content {
    margin: 13px 19px;
    line-height: 1.4
}

.leaflet-popup-content p {
    margin: 18px 0
}

.leaflet-popup-tip-container {
    width: 40px;
    height: 20px;
    position: absolute;
    left: 50%;
    margin-left: -20px;
    overflow: hidden;
    pointer-events: none
}

.leaflet-popup-tip {
    width: 17px;
    height: 17px;
    padding: 1px;
    margin: -10px auto 0;
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    transform: rotate(45deg)
}

.leaflet-popup-content-wrapper, .leaflet-popup-tip {
    background: white;
    color: #333;
    box-shadow: 0 3px 14px rgba(0, 0, 0, 0.4)
}

.leaflet-container a.leaflet-popup-close-button {
    position: absolute;
    top: 0;
    right: 0;
    padding: 4px 4px 0 0;
    border: 0;
    text-align: center;
    width: 18px;
    height: 14px;
    font: 16px/14px Tahoma, Verdana, sans-serif;
    color: #c3c3c3;
    text-decoration: none;
    font-weight: bold;
    background: transparent
}

.leaflet-container a.leaflet-popup-close-button:hover {
    color: #999
}

.leaflet-popup-scrolled {
    overflow: auto;
    border-bottom: 1px solid #ddd;
    border-top: 1px solid #ddd
}

.leaflet-oldie .leaflet-popup-content-wrapper {
    zoom: 1
}

.leaflet-oldie .leaflet-popup-tip {
    width: 24px;
    margin: 0 auto;
    -ms-filter: "progid:DXImageTransform.Microsoft.Matrix(M11=0.70710678, M12=0.70710678, M21=-0.70710678, M22=0.70710678)";
    filter: progid:DXImageTransform.Microsoft.Matrix(M11=0.70710678, M12=0.70710678, M21=-0.70710678, M22=0.70710678)
}

.leaflet-oldie .leaflet-popup-tip-container {
    margin-top: -1px
}

.leaflet-oldie .leaflet-control-zoom, .leaflet-oldie .leaflet-control-layers, .leaflet-oldie .leaflet-popup-content-wrapper, .leaflet-oldie .leaflet-popup-tip {
    border: 1px solid #999
}

.leaflet-div-icon {
    background: #fff;
    border: 1px solid #666
}

.leaflet-tooltip {
    position: absolute;
    padding: 6px;
    background-color: #fff;
    border: 1px solid #fff;
    border-radius: 3px;
    color: #222;
    white-space: nowrap;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    pointer-events: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4)
}

.leaflet-tooltip.leaflet-clickable {
    cursor: pointer;
    pointer-events: auto
}

.leaflet-tooltip-top:before, .leaflet-tooltip-bottom:before, .leaflet-tooltip-left:before, .leaflet-tooltip-right:before {
    position: absolute;
    pointer-events: none;
    border: 6px solid transparent;
    background: transparent;
    content: ""
}

.leaflet-tooltip-bottom {
    margin-top: 6px
}

.leaflet-tooltip-top {
    margin-top: -6px
}

.leaflet-tooltip-bottom:before, .leaflet-tooltip-top:before {
    left: 50%;
    margin-left: -6px
}

.leaflet-tooltip-top:before {
    bottom: 0;
    margin-bottom: -12px;
    border-top-color: #fff
}

.leaflet-tooltip-bottom:before {
    top: 0;
    margin-top: -12px;
    margin-left: -6px;
    border-bottom-color: #fff
}

.leaflet-tooltip-left {
    margin-left: -6px
}

.leaflet-tooltip-right {
    margin-left: 6px
}

.leaflet-tooltip-left:before, .leaflet-tooltip-right:before {
    top: 50%;
    margin-top: -6px
}

.leaflet-tooltip-left:before {
    right: 0;
    margin-right: -12px;
    border-left-color: #fff
}

.leaflet-tooltip-right:before {
    left: 0;
    margin-left: -12px;
    border-right-color: #fff
}

.leaflet-label {
    background: #ebebeb;
    background: rgba(235, 235, 235, 0.81);
    background-clip: padding-box;
    border-color: #777;
    border-color: rgba(0, 0, 0, 0.25);
    border-radius: 4px;
    border-style: solid;
    border-width: 4px;
    color: #111;
    display: block;
    font: 12px/20px "Helvetica Neue", Arial, Helvetica, sans-serif;
    font-weight: bold;
    padding: 1px 6px;
    position: absolute;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    white-space: nowrap;
    z-index: 6
}

.leaflet-label.leaflet-clickable {
    cursor: pointer
}

.leaflet-label:before, .leaflet-label:after {
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
    content: none;
    position: absolute;
    top: 5px
}

.leaflet-label:before {
    border-right: 6px solid black;
    border-right-color: inherit;
    left: -10px
}

.leaflet-label:after {
    border-left: 6px solid black;
    border-left-color: inherit;
    right: -10px
}

.leaflet-label-right:before, .leaflet-label-left:after {
    content: ""
}

.leaflet-draw-section {
    position: relative
}

.leaflet-draw-toolbar {
    margin-top: 12px
}

.leaflet-draw-toolbar-top {
    margin-top: 0
}

.leaflet-draw-toolbar-notop a:first-child {
    border-top-right-radius: 0
}

.leaflet-draw-toolbar-nobottom a:last-child {
    border-bottom-right-radius: 0
}

.leaflet-draw-toolbar a {
    background-image: url("/App_Data/javascript/mapengine/draw/images/spritesheet.png");
    background-repeat: no-repeat
}

.leaflet-retina .leaflet-draw-toolbar a {
    background-image: url("/App_Data/javascript/mapengine/draw/images/spritesheet-2x.png");
    background-size: 210px 30px
}

.leaflet-draw a {
    display: block;
    text-align: center;
    text-decoration: none
}

.leaflet-draw-actions {
    display: none;
    list-style: none;
    margin: 0;
    padding: 0;
    position: absolute;
    left: 26px;
    top: 0
}

.leaflet-right .leaflet-draw-actions {
    right: 26px;
    left: auto
}

.leaflet-draw-actions li {
    display: inline-block
}

.leaflet-draw-actions li:first-child a {
    border-left: none
}

.leaflet-draw-actions li:last-child a {
    -webkit-border-radius: 0 4px 4px 0;
    border-radius: 0 4px 4px 0
}

.leaflet-right .leaflet-draw-actions li:last-child a {
    -webkit-border-radius: 0;
    border-radius: 0
}

.leaflet-right .leaflet-draw-actions li:first-child a {
    -webkit-border-radius: 4px 0 0 4px;
    border-radius: 4px 0 0 4px
}

.leaflet-draw-actions a {
    background-color: #919187;
    border-left: 1px solid #AAA;
    color: #FFF;
    font: 11px/19px "Helvetica Neue", Arial, Helvetica, sans-serif;
    line-height: 28px;
    text-decoration: none;
    padding-left: 10px;
    padding-right: 10px;
    height: 28px
}

.leaflet-draw-actions-bottom {
    margin-top: 0;
    white-space: nowrap
}

.leaflet-draw-actions-top {
    margin-top: 1px;
    white-space: nowrap
}

.leaflet-draw-actions-top a, .leaflet-draw-actions-bottom a {
    height: 27px;
    line-height: 27px
}

.leaflet-draw-actions a:hover {
    background-color: #A0A098
}

.leaflet-draw-actions-top.leaflet-draw-actions-bottom a {
    height: 26px;
    line-height: 26px
}

.leaflet-draw-toolbar .leaflet-draw-draw-polyline {
    background-position: -2px -2px
}

.leaflet-draw-toolbar .leaflet-draw-draw-polygon {
    background-position: -31px -2px
}

.leaflet-draw-toolbar .leaflet-draw-draw-rectangle {
    background-position: -62px -2px
}

.leaflet-draw-toolbar .leaflet-draw-draw-circle {
    background-position: -92px -2px
}

.leaflet-draw-toolbar .leaflet-draw-draw-marker {
    background-position: -122px -2px
}

.leaflet-draw-toolbar .leaflet-draw-edit-edit {
    background-position: -152px -2px
}

.leaflet-draw-toolbar .leaflet-draw-edit-remove {
    background-position: -182px -2px
}

.leaflet-draw-toolbar .leaflet-draw-edit-edit.leaflet-disabled {
    background-position: -212px -2px
}

.leaflet-draw-toolbar .leaflet-draw-edit-remove.leaflet-disabled {
    background-position: -242px -2px
}

.leaflet-mouse-marker {
    background-color: #fff;
    cursor: crosshair
}

.leaflet-draw-tooltip {
    background: #363636;
    background: rgba(0, 0, 0, 0.5);
    border: 1px solid transparent;
    -webkit-border-radius: 4px;
    border-radius: 4px;
    color: #fff;
    font: 12px/18px "Helvetica Neue", Arial, Helvetica, sans-serif;
    margin-left: 20px;
    margin-top: -21px;
    padding: 4px 8px;
    position: absolute;
    visibility: hidden;
    white-space: nowrap;
    z-index: 6
}

.leaflet-draw-tooltip:before {
    border-right: 6px solid black;
    border-right-color: rgba(0, 0, 0, 0.5);
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
    content: "";
    position: absolute;
    top: 7px;
    left: -7px
}

.leaflet-error-draw-tooltip {
    background-color: #F2DEDE;
    border: 1px solid #E6B6BD;
    color: #B94A48
}

.leaflet-error-draw-tooltip:before {
    border-right-color: #E6B6BD
}

.leaflet-draw-tooltip-single {
    margin-top: -12px
}

.leaflet-draw-tooltip-subtext {
    color: #f8d5e4
}

.leaflet-draw-guide-dash {
    font-size: 1%;
    opacity: .6;
    position: absolute;
    width: 5px;
    height: 5px
}

.leaflet-edit-marker-selected {
    background: rgba(254, 87, 161, 0.1);
    border: 4px dashed rgba(254, 87, 161, 0.6);
    -webkit-border-radius: 4px;
    border-radius: 4px
}

.leaflet-edit-move {
    cursor: move
}

.leaflet-edit-resize {
    cursor: pointer
}

.toast-container {
    width: 360px;
    z-index: 9999
}

* html .toast-container {
    position: absolute
}

.toast-item {
    height: auto;
    background: #4c4b4a;
    opacity: .9;
    -moz-border-radius: 10px;
    -webkit-border-radius: 10px;
    color: #eee;
    padding-top: 20px;
    padding-bottom: 20px;
    padding-left: 6px;
    padding-right: 6px;
    font-family: lucida Grande;
    font-size: 14px;
    border: 2px solid #999;
    display: block;
    position: relative;
    margin: 0 0 12px 0
}

.toast-item p {
    text-align: left;
    margin-left: 50px
}

.toast-item-image {
    width: 32px;
    height: 32px;
    margin-left: 10px;
    margin-top: 10px;
    margin-right: 10px;
    float: left
}

.toast-item-image-notice {
    background: url("/javascript/toastmessage/resources/images/notice.png")
}

.toast-item-image-success {
    background: url("/javascript/toastmessage/resources/images/success.png")
}

.toast-item-image-warning {
    background: url("/javascript/toastmessage/resources/images/warning.png")
}

.toast-item-image-error {
    background: url("/javascript/toastmessage/resources/images/error.png")
}

.toast-type-notice {
    color: white
}

.toast-type-success {
    color: white
}

.toast-type-warning {
    color: white;
    border-color: #FCBD57
}

.toast-type-error {
    color: white;
    border-color: #B32B2B
}

.toast-position-top-left {
    position: fixed;
    left: 20px;
    top: 20px
}

.toast-position-top-center {
    position: fixed;
    top: 20px;
    left: 50%;
    margin-left: -140px
}

.toast-position-top-right {
    position: fixed;
    top: 20px;
    right: 20px
}

.toast-position-middle-left {
    position: fixed;
    left: 20px;
    top: 50%;
    margin-top: -40px
}

.toast-position-middle-center {
    position: fixed;
    left: 50%;
    margin-left: -140px;
    margin-top: -40px;
    top: 50%
}

.toast-position-middle-right {
    position: fixed;
    right: 20px;
    margin-left: -140px;
    margin-top: -40px;
    top: 50%
}

.leaflet-grid-label .lng {
    margin-left: 8px;
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg)
}

.leaflet-grid-label .lat, .leaflet-grid-label
.lng {
    text-shadow: -2px 0 #FFF, 0 2px #FFF, 2px 0 #FFF, 0 -2px #FFF
}

.locus {
    background: url(../../img/map/locus.png);
    background-repeat: no-repeat;
    border: none;
    background-size: 32.5px 32.5px;

}


.animationButton {
    cursor: pointer;
    font-size: 1.5vh;
    width: 2vw;
    height: 2vw;
    border: none;
}

.inputtime {
    width: 8.8vw;
    height: 2vw;
    text-align: center;
    font-size: 1.8vh;
}

