$(function () {
    getNowFormatDate();
});

//获取当前时间
function getNowFormatDate() {
    let date = new Date();
    let year = date.getFullYear();
    let month = date.getMonth() + 1;
    let strDate = date.getDate();
    let hour = date.getHours(); // 获取当前小时数(0-23)
    let minute = date.getMinutes(); // 获取当前分钟数(0-59)
    let second = date.getSeconds(); // 获取当前秒数(0-59)
    let show_day = new Array('星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六');
    let day = date.getDay();
    if (hour < 10) {
        hour = "0" + hour;
    }
    if (minute < 10) {
        minute = "0" + minute;
    }
    if (second < 10) {
        second = "0" + second;
    }
    if (month >= 1 && month <= 9) {
        month = "0" + month;
    }
    if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
    }
    let HMS = '' + hour + ':' + minute + ':' + second + '';
    // $('.nowTime li:nth-child(1)').html(HMS);
    // $('.dataNowtime').html(year + '年' + month + '月' + strDate + "日");
    // $('.dataNowd').html(year + '.' + month + '.' + strDate + ' ' + show_day[day]);
    $(".leftYmd").html(year + '.' + month + '.' + strDate + ' ' + show_day[day]);
    $('.leftTop').html(HMS)

    hour=date.getUTCHours()
    minute=date.getUTCMinutes()
    second=date.getUTCSeconds()
    if (hour < 10) {
        hour = "0" + hour;
    }
    if (minute < 10) {
        minute = "0" + minute;
    }
    if (second < 10) {
        second = "0" + second;
    }
    let UTC = '' + hour + ':' + minute + ':' + second + '';
    $('.serverTime').html(UTC)
    $(".rightYmd").html(year + '.' + month + '.' + strDate + ' ' + show_day[day]);
    setTimeout("getNowFormatDate()", 1000); //每隔1秒重新调用一次该函数
}


//经度格式化
function formatLong(value) {
    if(value > 180){
        value = value - 360;
    }
    let str;
    if (value) {
        let pos = posMatter(value);
        if (pos[3] === 0) {
            pos[3] = 'E';
        } else {
            pos[3] = 'W';
        }
        str = String(pos[0]) + '°' + String(pos[1]) + '\'' + String(pos[2]) + '\"' + pos[3];
    } else if (value === 0) {
        str = '0°0\'0\"';
    } else {
        str = '';
    }
    return str;
}

//纬度格式化
function formatLat(value) {
    let str;
    if (value) {
        let pos = posMatter(value);
        if (pos[3] === 0) {
            pos[3] = 'N';
        } else {
            pos[3] = 'S';
        }
        str = String(pos[0]) + '°' + String(pos[1]) + '\'' + String(pos[2]) + '\"' + pos[3];
    } else if (value === 0) {
        str = '0°0\'0\"';
    } else {
        str = '';
    }
    return str;
}

//保留两位小数，不足补0
function changeTwoDecimal_f(x, y) {
    let f_x = parseFloat(x);
    if (isNaN(f_x)) {
        return 0;
    }
    f_x = Math.round(x * Math.pow(10, y)) / Math.pow(10, y);
    let s_x = f_x.toString();
    let pos_decimal = s_x.indexOf('.');
    if (pos_decimal < 0) {
        pos_decimal = s_x.length;
        s_x += '.';
    }
    while (s_x.length <= pos_decimal + y) {
        s_x += '0';
    }
    return s_x;
}

// 经纬度取度、分、秒、方位
function posMatter(value) {
    let pos_d, pos_f, pos_m, pos_s, pos;
    // 经度 度、分、秒赋值
    pos = value * 3600 * 60;
    if (pos < 0) {
        pos_s = 1;
        pos *= -1;
    } else {
        pos_s = 0;
    }
    pos_d = Math.floor(pos / 3600 / 60);
    pos %= 216000;
    pos_f = Math.floor(pos / 3600);
    if (pos_f < 10) {
        pos_f = "0" + pos_f
    }
    pos %= 3600;
    pos_m = Math.floor(pos / 60 * 1000) / 1000;
    pos_m = changeTwoDecimal_f(pos_m, 3);
    if (pos_m < 10) {
        pos_m = "0" + pos_m
    }
    return [pos_d, pos_f, pos_m, pos_s];
}

//时间格式
function transformTime(timestamp) {
    let d = new Date(timestamp);
    let year = d.getFullYear();
    year = year < 10 ? '0' + year : year;
    let month = d.getMonth() + 1;
    month = month < 10 ? '0' + month : month;
    let date = d.getDate();
    date = date < 10 ? '0' + date : date;
    let hour = d.getHours();
    hour = hour < 10 ? '0' + hour : hour;
    let minutes = d.getMinutes();
    minutes = minutes < 10 ? '0' + minutes : minutes;
    let second = d.getSeconds();
    second = second < 10 ? '0' + second : second;
    return year + '-' + month + '-' + date + ' ' + hour + ':' + minutes + ':' + second;
}

//经纬度中的日期
function formatDate(value) {
    let date = new Date(value);
    let y = date.getFullYear();
    let MM = date.getMonth() + 1;
    MM = MM < 10 ? ('0' + MM) : MM;
    let d = date.getDate();
    d = d < 10 ? ('0' + d) : d;
    let h = date.getHours();
    h = h < 10 ? ('0' + h) : h;
    let m = date.getMinutes();
    m = m < 10 ? ('0' + m) : m;
    let s = date.getSeconds();
    s = s < 10 ? ('0' + s) : s;
    return y + '-' + MM + '-' + d + ' ' + h + ':' + m + ':' + s;
}

//判空
function isEmpty(obj) {
    return obj === undefined || obj === null || obj === "" || obj === "null";
}
