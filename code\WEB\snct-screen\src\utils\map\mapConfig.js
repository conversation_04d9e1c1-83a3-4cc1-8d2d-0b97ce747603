/**
 * 地图配置文件
 * 包含地图的基础配置、图层配置、样式配置等
 */

import { mapHost, mapAccessToken, zoomLevel } from '@/utils/host'

// 地图基础配置
export const mapConfig = {
  // 地图服务器地址
  mapHost: mapHost,
  
  // 访问令牌
  accessToken: mapAccessToken,
  
  // 默认缩放级别
  defaultZoom: zoomLevel,
  
  // 默认中心点
  defaultCenter: [120, 30],
  
  // 地图默认选项
  defaultOptions: {
    zoomControl: true,
    scaleControl: true,
    overviewMapControl: false,
    mapTypeControl: true,
    navigationControl: true,
    enableScrollWheelZoom: true,
    enableDoubleClickZoom: true,
    enableKeyboard: true,
    enableInertialDragging: true,
    enableContinuousZoom: true,
    enablePinchToZoom: true,
    enableAutoResize: true,
    minZoom: 3,
    maxZoom: 18
  },

  // 图层配置
  layers: {
    // 卫星图层
    satellite: {
      name: '卫星图',
      url: `${mapHost}/tile/satellite/{z}/{x}/{y}.png`,
      type: 'tile',
      attribution: 'Bigemap Satellite'
    },
    
    // 海图图层
    sea: {
      name: '海图',
      url: `${mapHost}/tile/sea/{z}/{x}/{y}.png`,
      type: 'tile',
      attribution: 'Bigemap Sea Chart'
    },
    
    // 街道图层
    street: {
      name: '街道图',
      url: `${mapHost}/tile/street/{z}/{x}/{y}.png`,
      type: 'tile',
      attribution: 'Bigemap Street'
    },
    
    // 地形图层
    terrain: {
      name: '地形图',
      url: `${mapHost}/tile/terrain/{z}/{x}/{y}.png`,
      type: 'tile',
      attribution: 'Bigemap Terrain'
    }
  },

  // 船舶图标配置
  shipIcons: {
    // 默认船舶图标
    default: {
      url: '/img/ship/ship.png',
      size: [32, 32],
      anchor: [16, 16]
    },
    
    // 不同状态的船舶图标
    active: {
      url: '/img/ship/ship-active.png',
      size: [36, 36],
      anchor: [18, 18]
    },
    
    // 离线船舶图标
    offline: {
      url: '/img/ship/ship-offline.png',
      size: [28, 28],
      anchor: [14, 14]
    },
    
    // 警告状态图标
    warning: {
      url: '/img/ship/ship-warning.png',
      size: [32, 32],
      anchor: [16, 16]
    },
    
    // 按颜色分类的图标
    colors: {
      '#FFDB6F': '/img/ship/position0.png',
      '#23FDF0': '/img/ship/position1.png',
      '#8AAFEC': '/img/ship/position2.png',
      '#FF8955': '/img/ship/position3.png',
      '#21a40a': '/img/ship/position4.png'
    }
  },

  // 轨迹颜色配置
  trackColors: [
    '#FFDB6F', // 黄色
    '#23FDF0', // 青色
    '#8AAFEC', // 蓝色
    '#FF8955', // 橙色
    '#21a40a', // 绿色
    '#ff6b6b', // 红色
    '#4ecdc4', // 蓝绿色
    '#45b7d1', // 天蓝色
    '#f9ca24', // 金黄色
    '#6c5ce7'  // 紫色
  ],

  // 轨迹样式配置
  trackStyles: {
    default: {
      strokeWeight: 3,
      strokeOpacity: 0.8,
      strokeStyle: 'solid'
    },
    
    active: {
      strokeWeight: 4,
      strokeOpacity: 1.0,
      strokeStyle: 'solid'
    },
    
    historical: {
      strokeWeight: 2,
      strokeOpacity: 0.6,
      strokeStyle: 'dashed'
    }
  },

  // 聚合配置
  cluster: {
    enabled: true,
    gridSize: 60,
    maxZoom: 15,
    styles: [{
      url: '/img/cluster/cluster1.png',
      height: 53,
      width: 53,
      textColor: '#fff',
      textSize: 12
    }, {
      url: '/img/cluster/cluster2.png',
      height: 56,
      width: 56,
      textColor: '#fff',
      textSize: 12
    }, {
      url: '/img/cluster/cluster3.png',
      height: 66,
      width: 66,
      textColor: '#fff',
      textSize: 12
    }]
  },

  // 弹窗配置
  popup: {
    maxWidth: 300,
    minWidth: 200,
    offset: [0, -10],
    closeButton: true,
    autoPan: true,
    className: 'ship-popup'
  },

  // 控件配置
  controls: {
    zoom: {
      position: 'topright',
      zoomInText: '+',
      zoomOutText: '-'
    },
    
    scale: {
      position: 'bottomleft',
      metric: true,
      imperial: false
    },
    
    attribution: {
      position: 'bottomright',
      prefix: 'Bigemap'
    }
  },

  // 动画配置
  animation: {
    // 船舶移动动画
    shipMove: {
      duration: 1000,
      easing: 'linear'
    },
    
    // 轨迹绘制动画
    trackDraw: {
      duration: 100,
      easing: 'linear'
    },
    
    // 缩放动画
    zoom: {
      duration: 500,
      easing: 'ease-out'
    }
  },

  // 性能配置
  performance: {
    // 最大显示的船舶数量
    maxShips: 1000,
    
    // 最大轨迹点数
    maxTrackPoints: 5000,
    
    // 更新频率（毫秒）
    updateInterval: 1000,
    
    // 是否启用聚合
    enableClustering: true,
    
    // 聚合阈值
    clusterThreshold: 50
  },

  // 九段线配置
  nineLine: {
    strokeColor: '#ffffff',
    strokeWeight: 2,
    strokeOpacity: 1.0,
    fillOpacity: 0,
    zIndex: 1000
  },

  // 气象图层配置
  weather: {
    wind: {
      enabled: false,
      opacity: 0.6,
      updateInterval: 300000 // 5分钟
    },
    
    temperature: {
      enabled: false,
      opacity: 0.5,
      updateInterval: 600000 // 10分钟
    },
    
    precipitation: {
      enabled: false,
      opacity: 0.7,
      updateInterval: 300000 // 5分钟
    }
  },

  // 测量工具配置
  measurement: {
    distance: {
      strokeColor: '#ff0000',
      strokeWeight: 2,
      strokeOpacity: 0.8
    },
    
    area: {
      strokeColor: '#ff0000',
      strokeWeight: 2,
      strokeOpacity: 0.8,
      fillColor: '#ff0000',
      fillOpacity: 0.2
    }
  },

  // 绘制工具配置
  drawing: {
    marker: {
      draggable: true,
      icon: '/img/markers/custom-marker.png'
    },
    
    polyline: {
      strokeColor: '#0000ff',
      strokeWeight: 3,
      strokeOpacity: 0.8
    },
    
    polygon: {
      strokeColor: '#0000ff',
      strokeWeight: 2,
      strokeOpacity: 0.8,
      fillColor: '#0000ff',
      fillOpacity: 0.2
    },
    
    circle: {
      strokeColor: '#00ff00',
      strokeWeight: 2,
      strokeOpacity: 0.8,
      fillColor: '#00ff00',
      fillOpacity: 0.2
    }
  }
}

// 获取图层配置
export function getLayerConfig(layerName) {
  return mapConfig.layers[layerName] || null
}

// 获取船舶图标配置
export function getShipIconConfig(type = 'default') {
  return mapConfig.shipIcons[type] || mapConfig.shipIcons.default
}

// 获取轨迹颜色
export function getTrackColor(index = 0) {
  const colors = mapConfig.trackColors
  return colors[index % colors.length]
}

// 获取轨迹样式配置
export function getTrackStyleConfig(type = 'default') {
  return mapConfig.trackStyles[type] || mapConfig.trackStyles.default
}

// 验证地图配置
export function validateMapConfig() {
  const required = ['mapHost', 'defaultZoom', 'defaultCenter']
  const missing = required.filter(key => !mapConfig[key])
  
  if (missing.length > 0) {
    console.warn('地图配置缺少必要参数:', missing)
    return false
  }
  
  return true
}

export default mapConfig
