<template>
  <div class="map-test-container">
    <div class="header">
      <h2>BigMap组件测试页面</h2>
      <div class="controls">
        <el-button @click="changeCenter" type="primary">切换中心点</el-button>
        <el-button @click="addTestMarker" type="success">添加标记</el-button>
        <el-button @click="clearMarkers" type="warning">清除标记</el-button>
      </div>
    </div>
    
    <div class="map-container">
      <BigMap
        ref="bigMap"
        :center="mapCenter"
        :zoom="mapZoom"
        :height="'600px'"
        :show-controls="true"
        @map-ready="onMapReady"
        @map-error="onMapError"
        @map-click="onMapClick"
        @layer-changed="onLayerChanged"
      />
    </div>
    
    <div class="info-panel">
      <h3>地图信息</h3>
      <p><strong>状态:</strong> {{ mapStatus }}</p>
      <p><strong>中心点:</strong> {{ mapCenter.join(', ') }}</p>
      <p><strong>缩放级别:</strong> {{ mapZoom }}</p>
      <p><strong>点击位置:</strong> {{ clickPosition }}</p>
      <p><strong>当前图层:</strong> {{ currentLayer }}</p>
      <p><strong>标记数量:</strong> {{ markers.length }}</p>
    </div>
  </div>
</template>

<script>
import BigMap from '@/components/map/BigMap.vue'

export default {
  name: 'MapTest',
  components: {
    BigMap
  },
  data() {
    return {
      mapCenter: [120.0, 30.0],
      mapZoom: 7,
      mapStatus: '初始化中...',
      clickPosition: '未点击',
      currentLayer: 'satellite',
      markers: [],
      testCenters: [
        [120.0, 30.0], // 中国海域
        [118.1, 24.5], // 厦门
        [121.5, 31.2], // 上海
        [113.3, 23.1]  // 广州
      ],
      currentCenterIndex: 0
    }
  },
  methods: {
    onMapReady(map) {
      this.mapStatus = '地图加载成功'
      console.log('地图初始化完成:', map)
    },
    
    onMapError(error) {
      this.mapStatus = '地图加载失败: ' + error.message
      console.error('地图初始化失败:', error)
    },
    
    onMapClick(event) {
      if (event.latlng) {
        this.clickPosition = `${event.latlng.lat.toFixed(4)}, ${event.latlng.lng.toFixed(4)}`
      }
      console.log('地图点击事件:', event)
    },
    
    onLayerChanged(layerKey) {
      this.currentLayer = layerKey
      console.log('图层切换:', layerKey)
    },
    
    changeCenter() {
      this.currentCenterIndex = (this.currentCenterIndex + 1) % this.testCenters.length
      this.mapCenter = [...this.testCenters[this.currentCenterIndex]]
      
      // 使用组件方法设置中心点
      if (this.$refs.bigMap) {
        this.$refs.bigMap.setCenter(this.mapCenter[0], this.mapCenter[1], this.mapZoom)
      }
    },
    
    addTestMarker() {
      if (this.$refs.bigMap) {
        const randomLng = this.mapCenter[0] + (Math.random() - 0.5) * 2
        const randomLat = this.mapCenter[1] + (Math.random() - 0.5) * 2
        
        const marker = this.$refs.bigMap.addMarker(randomLng, randomLat, {
          title: `标记 ${this.markers.length + 1}`
        })
        
        if (marker) {
          this.markers.push(marker)
          this.$message.success(`添加标记成功，位置: ${randomLng.toFixed(4)}, ${randomLat.toFixed(4)}`)
        }
      }
    },
    
    clearMarkers() {
      if (this.$refs.bigMap) {
        this.markers.forEach(marker => {
          this.$refs.bigMap.removeMarker(marker)
        })
        this.markers = []
        this.$message.success('已清除所有标记')
      }
    }
  }
}
</script>

<style scoped>
.map-test-container {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: #f5f5f5;
  border-radius: 4px;
}

.header h2 {
  margin: 0;
  color: #333;
}

.controls {
  display: flex;
  gap: 10px;
}

.map-container {
  flex: 1;
  border: 2px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.info-panel {
  margin-top: 20px;
  padding: 15px;
  background: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.info-panel h3 {
  margin-top: 0;
  color: #333;
}

.info-panel p {
  margin: 8px 0;
  color: #666;
}
</style>
