<template>
  <div class="ship-marker-container">
    <!-- 船舶信息弹窗 -->
    <div 
      v-if="showPopup && shipInfo" 
      class="ship-popup"
      :style="popupStyle"
    >
      <div class="popup-header">
        <h4>{{ shipInfo.name || '未知船舶' }}</h4>
        <i class="el-icon-close" @click="closePopup"></i>
      </div>
      <div class="popup-content">
        <div class="info-row">
          <span class="label">经度:</span>
          <span class="value">{{ shipInfo.longitude }}°</span>
        </div>
        <div class="info-row">
          <span class="label">纬度:</span>
          <span class="value">{{ shipInfo.latitude }}°</span>
        </div>
        <div class="info-row">
          <span class="label">船速:</span>
          <span class="value">{{ shipInfo.speed || 0 }} 节</span>
        </div>
        <div class="info-row">
          <span class="label">船向:</span>
          <span class="value">{{ shipInfo.heading || 0 }}°</span>
        </div>
        <div class="info-row">
          <span class="label">时间:</span>
          <span class="value">{{ formatTime(shipInfo.timestamp) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapUtils } from '@/utils/map/mapUtils'

export default {
  name: 'ShipMarker',
  props: {
    // 地图实例
    map: {
      type: Object,
      required: true
    },
    // 船舶信息
    shipInfo: {
      type: Object,
      required: true,
      validator(value) {
        return value.longitude !== undefined && value.latitude !== undefined
      }
    },
    // 船舶图标
    icon: {
      type: String,
      default: '/img/ship/ship.png'
    },
    // 图标大小
    iconSize: {
      type: Array,
      default: () => [32, 32]
    },
    // 是否可拖拽
    draggable: {
      type: Boolean,
      default: false
    },
    // 是否显示轨迹
    showTrack: {
      type: Boolean,
      default: false
    },
    // 轨迹颜色
    trackColor: {
      type: String,
      default: '#ff6b6b'
    },
    // 是否自动旋转图标（根据船向）
    autoRotate: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      marker: null,
      trackPolyline: null,
      trackPoints: [],
      showPopup: false,
      popupStyle: {}
    }
  },
  watch: {
    shipInfo: {
      handler(newInfo, oldInfo) {
        this.updateMarkerPosition(newInfo, oldInfo)
      },
      deep: true
    },
    icon() {
      this.updateMarkerIcon()
    }
  },
  mounted() {
    this.createMarker()
  },
  beforeDestroy() {
    this.removeMarker()
  },
  methods: {
    // 创建船舶标记
    createMarker() {
      if (!this.map || !this.shipInfo) return

      const position = new BM.LngLat(this.shipInfo.longitude, this.shipInfo.latitude)
      
      // 创建自定义图标
      const icon = new BM.Icon({
        url: this.icon,
        size: new BM.Size(this.iconSize[0], this.iconSize[1]),
        anchor: new BM.Size(this.iconSize[0] / 2, this.iconSize[1] / 2)
      })

      // 创建标记
      this.marker = new BM.Marker(position, {
        icon: icon,
        draggable: this.draggable
      })

      // 设置船舶朝向
      if (this.autoRotate && this.shipInfo.heading !== undefined) {
        this.setMarkerRotation(this.shipInfo.heading)
      }

      // 绑定事件
      this.bindMarkerEvents()

      // 添加到地图
      this.map.addOverlay(this.marker)

      // 初始化轨迹
      if (this.showTrack) {
        this.initTrack()
      }
    },

    // 绑定标记事件
    bindMarkerEvents() {
      if (!this.marker) return

      // 点击事件
      this.marker.addEventListener('click', (e) => {
        this.showShipPopup(e)
        this.$emit('marker-click', this.shipInfo, e)
      })

      // 拖拽事件
      if (this.draggable) {
        this.marker.addEventListener('dragend', (e) => {
          const position = this.marker.getPosition()
          this.$emit('marker-dragend', {
            longitude: position.lng,
            latitude: position.lat
          })
        })
      }
    },

    // 更新标记位置
    updateMarkerPosition(newInfo, oldInfo) {
      if (!this.marker || !newInfo) return

      const newPosition = new BM.LngLat(newInfo.longitude, newInfo.latitude)
      
      // 平滑移动到新位置
      if (oldInfo && this.isValidPosition(oldInfo)) {
        this.animateMarkerMove(newPosition)
      } else {
        this.marker.setPosition(newPosition)
      }

      // 更新船舶朝向
      if (this.autoRotate && newInfo.heading !== undefined) {
        this.setMarkerRotation(newInfo.heading)
      }

      // 更新轨迹
      if (this.showTrack) {
        this.addTrackPoint(newPosition)
      }

      this.$emit('position-updated', newInfo)
    },

    // 动画移动标记
    animateMarkerMove(targetPosition) {
      if (!this.marker) return

      const currentPosition = this.marker.getPosition()
      const duration = 1000 // 1秒动画
      const steps = 30
      const stepDuration = duration / steps

      let step = 0
      const animate = () => {
        step++
        const progress = step / steps
        
        // 线性插值计算中间位置
        const lng = currentPosition.lng + (targetPosition.lng - currentPosition.lng) * progress
        const lat = currentPosition.lat + (targetPosition.lat - currentPosition.lat) * progress
        
        this.marker.setPosition(new BM.LngLat(lng, lat))
        
        if (step < steps) {
          setTimeout(animate, stepDuration)
        }
      }
      
      animate()
    },

    // 设置标记旋转角度
    setMarkerRotation(heading) {
      if (!this.marker) return
      
      // 使用 CSS 变换旋转图标
      const markerElement = this.marker.getElement()
      if (markerElement) {
        markerElement.style.transform = `rotate(${heading}deg)`
        markerElement.style.transformOrigin = 'center center'
      }
    },

    // 更新标记图标
    updateMarkerIcon() {
      if (!this.marker) return

      const icon = new BM.Icon({
        url: this.icon,
        size: new BM.Size(this.iconSize[0], this.iconSize[1]),
        anchor: new BM.Size(this.iconSize[0] / 2, this.iconSize[1] / 2)
      })

      this.marker.setIcon(icon)
    },

    // 初始化轨迹
    initTrack() {
      this.trackPoints = []
      if (this.shipInfo) {
        const position = new BM.LngLat(this.shipInfo.longitude, this.shipInfo.latitude)
        this.trackPoints.push(position)
      }
    },

    // 添加轨迹点
    addTrackPoint(position) {
      this.trackPoints.push(position)
      
      // 限制轨迹点数量，避免内存泄漏
      if (this.trackPoints.length > 1000) {
        this.trackPoints = this.trackPoints.slice(-500)
      }
      
      this.updateTrackPolyline()
    },

    // 更新轨迹线
    updateTrackPolyline() {
      if (this.trackPoints.length < 2) return

      // 移除旧的轨迹线
      if (this.trackPolyline) {
        this.map.removeOverlay(this.trackPolyline)
      }

      // 创建新的轨迹线
      this.trackPolyline = new BM.Polyline(this.trackPoints, {
        strokeColor: this.trackColor,
        strokeWeight: 3,
        strokeOpacity: 0.8
      })

      this.map.addOverlay(this.trackPolyline)
    },

    // 显示船舶信息弹窗
    showShipPopup(e) {
      this.showPopup = true
      
      // 计算弹窗位置
      const pixel = this.map.pointToPixel(this.marker.getPosition())
      this.popupStyle = {
        left: pixel.x + 'px',
        top: (pixel.y - 150) + 'px'
      }
    },

    // 关闭弹窗
    closePopup() {
      this.showPopup = false
    },

    // 移除标记
    removeMarker() {
      if (this.marker) {
        this.map.removeOverlay(this.marker)
        this.marker = null
      }
      
      if (this.trackPolyline) {
        this.map.removeOverlay(this.trackPolyline)
        this.trackPolyline = null
      }
    },

    // 验证位置有效性
    isValidPosition(info) {
      return info && 
             typeof info.longitude === 'number' && 
             typeof info.latitude === 'number' &&
             !isNaN(info.longitude) && 
             !isNaN(info.latitude)
    },

    // 格式化时间
    formatTime(timestamp) {
      if (!timestamp) return '--'
      const date = new Date(timestamp)
      return date.toLocaleString('zh-CN')
    },

    // 获取标记实例
    getMarker() {
      return this.marker
    },

    // 清除轨迹
    clearTrack() {
      this.trackPoints = []
      if (this.trackPolyline) {
        this.map.removeOverlay(this.trackPolyline)
        this.trackPolyline = null
      }
    }
  }
}
</script>

<style scoped>
.ship-marker-container {
  position: relative;
}

.ship-popup {
  position: absolute;
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 200px;
  max-width: 300px;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #eee;
  background: #f8f9fa;
  border-radius: 6px 6px 0 0;
}

.popup-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.popup-header .el-icon-close {
  cursor: pointer;
  color: #999;
  font-size: 16px;
}

.popup-header .el-icon-close:hover {
  color: #666;
}

.popup-content {
  padding: 12px 15px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  color: #666;
  font-weight: 500;
}

.value {
  color: #333;
  font-weight: 600;
}
</style>
