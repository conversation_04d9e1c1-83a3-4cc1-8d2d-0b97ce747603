
/*
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-08-02 14:58:23
 */
import request, { GET, POST, PUT, DELETE, FILESubmit, FILE } from '@/utils/request'
import UtilVar from "@/config/UtilVar"

// 导出基础URL供其他模块使用
export const baseUrl = UtilVar.baseUrl

// 兼容旧的API调用方式，直接导出标准化的请求方法
export { GET, POST, PUT, DELETE, FILESubmit, FILE }

// 没有基地址的GET请求，访问根目录下文件
export const GETNOBASE = async (url, params) => {
  try {
    const data = await request.get(url, { params, baseURL: '' });
    return data;
  } catch (error) {
    return Promise.reject(error);
  }
}

// 默认导出request实例
export default request


