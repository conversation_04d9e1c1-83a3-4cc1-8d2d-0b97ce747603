<template>
  <div class="track-line-container">
    <!-- 轨迹控制面板 -->
    <div v-if="showControls" class="track-controls">
      <el-button 
        size="mini" 
        type="primary" 
        @click="toggleTrackVisibility"
        :icon="trackVisible ? 'el-icon-view' : 'el-icon-hide'"
      >
        {{ trackVisible ? '隐藏轨迹' : '显示轨迹' }}
      </el-button>
      
      <el-button 
        size="mini" 
        type="warning" 
        @click="clearTrack"
        icon="el-icon-delete"
      >
        清除轨迹
      </el-button>
      
      <el-color-picker 
        v-model="currentColor" 
        size="mini"
        @change="updateTrackColor"
      />
    </div>

    <!-- 轨迹信息显示 -->
    <div v-if="showInfo && trackInfo" class="track-info">
      <div class="info-item">
        <span>总距离: {{ trackInfo.totalDistance.toFixed(2) }} 海里</span>
      </div>
      <div class="info-item">
        <span>轨迹点数: {{ trackInfo.pointCount }}</span>
      </div>
      <div class="info-item">
        <span>时间跨度: {{ trackInfo.timeSpan }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { coordinateUtils } from '@/utils/map/coordinateUtils'

export default {
  name: 'TrackLine',
  props: {
    // 地图实例
    map: {
      type: Object,
      required: true
    },
    // 轨迹数据点
    trackPoints: {
      type: Array,
      default: () => []
    },
    // 轨迹颜色
    color: {
      type: String,
      default: '#ff6b6b'
    },
    // 线条宽度
    strokeWeight: {
      type: Number,
      default: 3
    },
    // 线条透明度
    strokeOpacity: {
      type: Number,
      default: 0.8
    },
    // 是否显示箭头
    showArrow: {
      type: Boolean,
      default: true
    },
    // 是否显示控制面板
    showControls: {
      type: Boolean,
      default: false
    },
    // 是否显示轨迹信息
    showInfo: {
      type: Boolean,
      default: false
    },
    // 是否启用动画
    animated: {
      type: Boolean,
      default: false
    },
    // 动画速度（毫秒）
    animationSpeed: {
      type: Number,
      default: 100
    }
  },
  data() {
    return {
      polyline: null,
      arrowPolyline: null,
      trackVisible: true,
      currentColor: this.color,
      animationTimer: null,
      currentAnimationIndex: 0,
      trackInfo: null
    }
  },
  watch: {
    trackPoints: {
      handler(newPoints) {
        this.updateTrackLine(newPoints)
      },
      deep: true
    },
    color(newColor) {
      this.currentColor = newColor
      this.updateTrackColor()
    }
  },
  mounted() {
    if (this.trackPoints.length > 0) {
      this.createTrackLine()
    }
  },
  beforeDestroy() {
    this.removeTrackLine()
    this.stopAnimation()
  },
  methods: {
    // 创建轨迹线
    createTrackLine() {
      if (!this.map || this.trackPoints.length < 2) return

      const points = this.convertToMapPoints(this.trackPoints)
      
      // 创建轨迹线
      this.polyline = new BM.Polyline(points, {
        strokeColor: this.currentColor,
        strokeWeight: this.strokeWeight,
        strokeOpacity: this.strokeOpacity
      })

      this.map.addOverlay(this.polyline)

      // 创建箭头（如果启用）
      if (this.showArrow) {
        this.createArrowPolyline(points)
      }

      // 计算轨迹信息
      this.calculateTrackInfo()

      // 启动动画（如果启用）
      if (this.animated) {
        this.startAnimation()
      }

      this.$emit('track-created', this.polyline)
    },

    // 更新轨迹线
    updateTrackLine(newPoints) {
      this.removeTrackLine()
      
      if (newPoints && newPoints.length > 0) {
        this.createTrackLine()
      }
    },

    // 创建带箭头的轨迹线
    createArrowPolyline(points) {
      if (points.length < 2) return

      // 计算箭头位置（每隔一定距离放置一个箭头）
      const arrowPoints = this.calculateArrowPoints(points)
      
      if (arrowPoints.length > 0) {
        this.arrowPolyline = new BM.Polyline(arrowPoints, {
          strokeColor: this.currentColor,
          strokeWeight: this.strokeWeight + 1,
          strokeOpacity: this.strokeOpacity,
          // 使用箭头样式
          icons: [{
            icon: {
              path: 'M 0,-1 0,1',
              strokeOpacity: 1,
              scale: 4
            },
            offset: '100%',
            repeat: '20px'
          }]
        })

        this.map.addOverlay(this.arrowPolyline)
      }
    },

    // 计算箭头点位置
    calculateArrowPoints(points) {
      const arrowPoints = []
      const minDistance = 0.01 // 最小距离（度）

      for (let i = 0; i < points.length - 1; i++) {
        const p1 = points[i]
        const p2 = points[i + 1]
        
        const distance = coordinateUtils.calculateDistance(
          p1.lat, p1.lng, p2.lat, p2.lng
        )
        
        if (distance > minDistance) {
          arrowPoints.push(p1, p2)
        }
      }

      return arrowPoints
    },

    // 转换为地图点格式
    convertToMapPoints(trackPoints) {
      return trackPoints.map(point => {
        if (point.longitude !== undefined && point.latitude !== undefined) {
          return new BM.LngLat(point.longitude, point.latitude)
        } else if (point.lng !== undefined && point.lat !== undefined) {
          return new BM.LngLat(point.lng, point.lat)
        } else if (Array.isArray(point) && point.length >= 2) {
          return new BM.LngLat(point[0], point[1])
        }
        return null
      }).filter(point => point !== null)
    },

    // 计算轨迹信息
    calculateTrackInfo() {
      if (this.trackPoints.length < 2) {
        this.trackInfo = null
        return
      }

      let totalDistance = 0
      let startTime = null
      let endTime = null

      for (let i = 0; i < this.trackPoints.length - 1; i++) {
        const p1 = this.trackPoints[i]
        const p2 = this.trackPoints[i + 1]
        
        // 计算距离
        const distance = coordinateUtils.calculateDistance(
          p1.latitude || p1.lat,
          p1.longitude || p1.lng,
          p2.latitude || p2.lat,
          p2.longitude || p2.lng
        )
        totalDistance += distance

        // 记录时间范围
        if (p1.timestamp) {
          if (!startTime || p1.timestamp < startTime) {
            startTime = p1.timestamp
          }
          if (!endTime || p1.timestamp > endTime) {
            endTime = p1.timestamp
          }
        }
      }

      this.trackInfo = {
        totalDistance: totalDistance,
        pointCount: this.trackPoints.length,
        timeSpan: this.formatTimeSpan(startTime, endTime)
      }
    },

    // 格式化时间跨度
    formatTimeSpan(startTime, endTime) {
      if (!startTime || !endTime) return '--'
      
      const start = new Date(startTime)
      const end = new Date(endTime)
      const duration = end - start
      
      const hours = Math.floor(duration / (1000 * 60 * 60))
      const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60))
      
      return `${hours}小时${minutes}分钟`
    },

    // 切换轨迹可见性
    toggleTrackVisibility() {
      this.trackVisible = !this.trackVisible
      
      if (this.polyline) {
        if (this.trackVisible) {
          this.map.addOverlay(this.polyline)
          if (this.arrowPolyline) {
            this.map.addOverlay(this.arrowPolyline)
          }
        } else {
          this.map.removeOverlay(this.polyline)
          if (this.arrowPolyline) {
            this.map.removeOverlay(this.arrowPolyline)
          }
        }
      }
    },

    // 更新轨迹颜色
    updateTrackColor() {
      if (this.polyline) {
        this.polyline.setOptions({
          strokeColor: this.currentColor
        })
      }
      
      if (this.arrowPolyline) {
        this.arrowPolyline.setOptions({
          strokeColor: this.currentColor
        })
      }
    },

    // 清除轨迹
    clearTrack() {
      this.removeTrackLine()
      this.$emit('track-cleared')
    },

    // 移除轨迹线
    removeTrackLine() {
      if (this.polyline) {
        this.map.removeOverlay(this.polyline)
        this.polyline = null
      }
      
      if (this.arrowPolyline) {
        this.map.removeOverlay(this.arrowPolyline)
        this.arrowPolyline = null
      }
      
      this.stopAnimation()
    },

    // 开始动画
    startAnimation() {
      if (!this.animated || this.trackPoints.length < 2) return
      
      this.currentAnimationIndex = 0
      this.animateTrack()
    },

    // 动画绘制轨迹
    animateTrack() {
      if (this.currentAnimationIndex >= this.trackPoints.length - 1) {
        this.$emit('animation-complete')
        return
      }

      const currentPoints = this.trackPoints.slice(0, this.currentAnimationIndex + 2)
      const mapPoints = this.convertToMapPoints(currentPoints)
      
      if (this.polyline) {
        this.map.removeOverlay(this.polyline)
      }
      
      this.polyline = new BM.Polyline(mapPoints, {
        strokeColor: this.currentColor,
        strokeWeight: this.strokeWeight,
        strokeOpacity: this.strokeOpacity
      })
      
      this.map.addOverlay(this.polyline)
      
      this.currentAnimationIndex++
      
      this.animationTimer = setTimeout(() => {
        this.animateTrack()
      }, this.animationSpeed)
    },

    // 停止动画
    stopAnimation() {
      if (this.animationTimer) {
        clearTimeout(this.animationTimer)
        this.animationTimer = null
      }
    },

    // 获取轨迹线实例
    getPolyline() {
      return this.polyline
    },

    // 适应轨迹范围
    fitTrackBounds() {
      if (!this.map || this.trackPoints.length === 0) return

      const bounds = new BM.LatLngBounds()
      this.trackPoints.forEach(point => {
        const lng = point.longitude || point.lng
        const lat = point.latitude || point.lat
        if (lng !== undefined && lat !== undefined) {
          bounds.extend(new BM.LngLat(lng, lat))
        }
      })

      this.map.fitBounds(bounds, { padding: 50 })
    }
  }
}
</script>

<style scoped>
.track-line-container {
  position: relative;
}

.track-controls {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.9);
  padding: 10px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  display: flex;
  gap: 8px;
  align-items: center;
}

.track-info {
  position: absolute;
  bottom: 10px;
  left: 10px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.9);
  padding: 10px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  font-size: 12px;
}

.info-item {
  margin-bottom: 4px;
}

.info-item:last-child {
  margin-bottom: 0;
}
</style>
