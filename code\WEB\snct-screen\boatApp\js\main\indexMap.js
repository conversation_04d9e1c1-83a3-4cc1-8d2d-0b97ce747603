let zoomLevel = 9;
// 在所有航次中的开始时间
let sTime;
// 在所有航次中的结束s时间
let eTime;
let html;
let shipName;
let pathPattern;
let ws = null;
//地图
let map;
//航次名称
let cruiseCode;
//船速
let hehdt;
//GPS
let gpsList = [];
//gps中的经纬度
let allCoordinate = [];
//小船
let shipMarker;
let moveTime = 0;
// websocket重连锁
let reconnectLock = false;

preventGrid();

$(function () {
    //获取最新航次信息
    getCruise();

    //获得艏向值
    getHehdtList();

    //获取GPS
    initGps();

    //初始化地图
    initMap();

    //抽取gps中的经纬度
    getCoordinate();

    //画轨迹
    drawPathLine();

    //创建websocket连接
    createWebSocket();

    toLesson();

});

//初始化地图
function initMap() {
    BM.Config.HTTP_URL = mapHost;

    map = BM.map('map', null, {
        crs: BM.CRS.EPSG4326,
        center: [24.550980383333332, 118.0971712],
        // center: [gpsList[gpsList.length - 1][1], gpsList[gpsList.length - 1][0]],
        zoom: zoomLevel,
        zoomControl: true,
        minZoom: 3,
        maxZoom: 13,
        attributionControl: false
    });
    //创建一个谷歌卫星图层 ，具体API详情请参见 ：http://www.bigemap.com/offlinemaps/api/#tilelayer
    let google_satellite = BM.tileLayer('bigemap.satellite');
    google_satellite.addTo(map);

    // 地图九段线添加
    nineLine("#D3D3D3");
    // 国境线添加
    frontier("#D3D3D3");

    //鼠标对地图的监控
    map.on("moveend", function (e) {
        zoomLevel = map.getZoom();
        moveTime = Math.round(new Date());
    });
}

//地图九段线添加
function nineLine(colorLine) {
    let geoJsonArray = getNineLineJson();
    BM.geoJSON(geoJsonArray, {
        style: function (feature) {
            return {color: colorLine, weight: 1};
        }
    }).addTo(map);
}

//  国境线添加
function frontier(colorLine) {
    let frontierJsonArray = getFrontierJson();
    BM.geoJSON(frontierJsonArray, {
        style: function (feature) {
            return {color: colorLine, weight: 1};
        }
    }).addTo(map);
}

//获取所有航次中的最新航次和历史数据起始时间
function getCruise() {
    let timestamp = Date.parse(new Date());
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/shipCruise/queryVoyageInfo/" + getCurrentSn() + '/' + timestamp,
        dataType: "json",
        async: false,
        success: function (result) {
            //航次初始时间
            sTime = result.startTime;

            eTime = Math.round(new Date() / 1000 * 1000);
            cruiseCode = result.code;
            shipName = result.shipName;
        }
    });
}

// 获取历史gps数据
function initGps() {
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/device/data/list",
        data: {
            sn: getCurrentSn(),
            deviceCode: '032A',
            startTime: sTime,
            endTime: eTime,
            interval: 5
        },
        dataType: "json",
        async: false,
        success: function (result) {
            let data = result.data;
            if (data !== undefined || data !== '' || data !== null) {
                gpsList = eval(data);
            }
        }
    });
}

//获取含有艏向值的数组
function getHehdtList() {
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/device/data/getLatestData",
        data: {
            sn: getCurrentSn(),
            deviceCode: '042A'
        },
        dataType: "json",
        async: false,
        success: function (result) {
            let data = result.data;
            if (data !== undefined && data !== '' && data !== null) {
                hehdt = parseInt(data.hehdt);
            }
        }
    });
}

//抽取gps中的经纬度
function getCoordinate() {
    if (gpsList === undefined || gpsList.length === 0) {
        return;
    }
    for (let i = 0; i < gpsList.length; i++) {
        let attr = [];
        attr.push(parseFloat(gpsList[i][1]));
        attr.push(parseFloat(gpsList[i][0]));
        allCoordinate.push(attr);
    }

    //画小船
    drawShipMarker(hehdt, gpsList[gpsList.length - 1]);
}

//画轨迹
function drawPathLine() {
    if (gpsList === undefined || gpsList.length === 0 || allCoordinate.length === 0) {
        return;
    }

    //绘线
    pathPattern = BM.polylineDecorator(
        allCoordinate,
        {
            patterns: [
                {
                    offset: 5,
                    repeat: 15,
                    symbol: BM.Symbol.dash({pixelSize: 9, pathOptions: {color: '#ff6347', weight: 1.5}})
                },
                {
                    offset: 43,
                    repeat: 70,
                    symbol: BM.Symbol.arrowHead({
                        headAngle: 40,
                        pixelSize: 15,
                        color: '#ff6347',
                        fillOpacity: 1,
                        weight: 1
                    })
                },
                {
                    offset: 0,
                    repeat: 70,
                    symbol: BM.Symbol.circleMarker({
                        radius: 3,
                        color: '#ff6347',
                        weight: 1.2,
                        fill: true,
                        fillColor: '#effffd',
                        fillOpacity: 1,
                        code: cruiseCode,
                        shipName: shipName
                    })
                },
            ]
        },
        gpsList
    ).addTo(map);
}


// 画船标记
function drawShipMarker(hehdt, gpsArr) {
    if (shipMarker !== undefined) {
        shipMarker.remove();
    }
    shipMarker = BM.marker([gpsArr[1], gpsArr[0]],
        {
            rotationAngle: hehdt,
            icon: BM.icon({
                // 会导致回放船向不再变动
                iconUrl: '../img/index/shipS.png',
                // 大小 x，y
                iconSize: [38, 38],
                // 偏移x,y
                iconAnchor: [20, 20],
                popupAnchor: [-3, -76],
            }),

        },
    ).addTo(map);

    shipMarker.on('mouseover', function (e) {
        if (gpsList.length === 0) {
            return;
        }
        let html = getShipMarkerHtml(shipName, cruiseCode, hehdt, gpsArr);
        shipMarker.bindTooltip(html).openTooltip();
    });
}

//船标记html
function getShipMarkerHtml(shipName, cruiseCode, hehdt, gpsArr) {
    //没数值时船图标初始弹窗
    let oneText = "<div class='popuoCss'>" +
        "<B>船名：</B>" + shipName + "\<br\>" +
        "<B>航次：</B>" + cruiseCode + "\<br\>" +
        "<B>航速：</B>" + 0 + "节" + "\<br\>" +
        "<B>艏向：</B>" + hehdt + "°" + "\<br\>" +
        '<B>距初始位置：</B>' + 0 + '海里\<br\>' +
        "<B>经纬度：</B>" + formatLat(gpsArr[1]) + "," + formatLong(gpsArr[0]) + "\<br\>" +
        "<B>数据时间：</B>" + transformTime(gpsArr[2]) + "\<br\></div>";

    if (gpsList.length <= 1) {
        shipMarker.bindTooltip(oneText).openTooltip();
    } else if (gpsArr.length < 5) {
        return "<div class='popuoCss'>" +
            "<B>船名：</B>" + shipName + "\<br\>" +
            "<B>航次：</B>" + cruiseCode + "\<br\>" +
            "<B>航速：</B>" + 0 + "节" + "\<br\>" +
            "<B>艏向：</B>" + hehdt + "°" + "\<br\>" +
            '<B>距初始位置：</B>' + gpsArr[4].toFixed(2) + '海里\<br\>' +
            "<B>经纬度：</B>" + formatLat(gpsArr[1]) + "," + formatLong(gpsArr[0]) + "\<br\>" +
            "<B>数据时间：</B>" + transformTime(gpsArr[2]) + "\<br\></div>";
    } else {
        return "<div class='popuoCss'>" +
            "<B>船名：</B>" + shipName + "\<br\>" +
            "<B>航次：</B>" + cruiseCode + "\<br\>" +
            "<B>航速：</B>" + gpsArr[3] + "节" + "\<br\>" +
            "<B>艏向：</B>" + hehdt + "°" + "\<br\>" +
            '<B>距初始位置：</B>' + gpsArr[4].toFixed(2) + '海里\<br\>' +
            "<B>经纬度：</B>" + formatLat(gpsArr[1]) + "," + formatLong(gpsArr[0]) + "\<br\>" +
            "<B>数据时间：</B>" + transformTime(gpsArr[2]) + "\<br\></div>";
    }
}

//按钮：点击图标显示隐藏
function setActive(obj) {
    $(".btn_weather").each(function (obj, index) {
        if ($(this).attr("is_show") === "1") {
            $(this).addClass("active");

        } else {
            $(this).removeClass("active");

        }
    });
}

// 轨迹弹出框信息
function toLoading() {
    $("#locus").on("click", function (e) {
        e.stopPropagation();
        if ($(this).attr("is_show") != null && $(this).attr("is_show") === "1") {
            $(this).attr("is_show", "0");
            $(".suspend").stop();
            $(".suspend").animate({width: "0vw"}, 300);
        } else {
            // 显示
            switchMap();
            $(".suspend").stop();
            $(".suspend").animate({width: "20.39vw"}, 300);
            $(this).attr("is_show", "1");
        }
        setActive($(this));
    });
}



// 创建websocket连接
function createWebSocket() {
    let codes = '032A,042A';
    let url = "ws://" + window.location.host + laputaWsHost + "/websocket/" + codes + "/" + getCurrentSn();
    try {
        if ('WebSocket' in window) {
            ws = new WebSocket(url);
        } else if ('MozWebSocket' in window) {
            ws = new MozWebSocket(url);
        } else {
            layui.use(['layer'], function () {
                let layer = layui.layer;
                layer.alert("您的浏览器不支持websocket协议,建议使用新版谷歌、火狐等浏览器，请勿使用IE10以下浏览器，360浏览器请使用极速模式，不要使用兼容模式！");
            });
        }
        initWebSocket();
    } catch (e) {
        reconnect(url);
    }
}

function initWebSocket() {
    ws.onopen = function () {
        //心跳检测重置
        heartCheck.reset().start();
    };

    ws.onmessage = function (e) {
        //拿到任何消息都说明当前连接是正常的
        heartCheck.reset().start();

        let currentTime = Math.round(new Date());
        //判断当前时间与操作时间的间隔，大于30秒则调取轨迹四周边界
        if (document.getElementById("restore").title === "暂停自动缩放") {
            if (currentTime > (moveTime + 1000 * 40) && gpsList.length > 2) {
                map.fitBounds(pathPattern.getBounds());
            }
        }


        let dataMap = JSON.parse(e.data);
        if (dataMap === undefined) {
            return;
        }
        //船向
        let hehdt;
        if (dataMap[getCurrentSn() + '_042A'] !== undefined && dataMap[getCurrentSn() + '_042A'] !== null) {
            hehdt = parseInt(dataMap[getCurrentSn() + '_042A'].hehdt);

        }
        if(gpsList === undefined || gpsList === null || gpsList === '') {
            return;
        }
        let gpsData;
        if (dataMap[getCurrentSn() + '_032A'] !== undefined && dataMap[getCurrentSn() + '_032A'] !== null) {
            gpsData = dataMap[getCurrentSn() + '_032A'];
        }

        if (gpsList.length === 0 || (gpsData.initialTime - gpsList[gpsList.length - 1][2]) > 5 * 60 * 1000) {
            // 添加坐标
            let arr = [];
            arr.push(parseFloat(gpsData.latitude));
            arr.push(parseFloat(gpsData.longitude));
            allCoordinate.push(arr);

            let tempArr = [];
            tempArr.push(parseFloat(gpsData.longitude).toFixed(7));
            tempArr.push(parseFloat(gpsData.latitude).toFixed(7));
            tempArr.push(parseInt(gpsData.initialTime));
            if (gpsData.groundRateJ !== null) {
                tempArr.push(gpsData.groundRateJ);
            }
            // 加上离起点距离
            let dis = (map.distance(allCoordinate[0], arr)) / 1852;
            tempArr.push(dis);
            gpsList.push(tempArr);

            //重新绘制轨迹
            drawPathLine();
        }
        drawShipMarker(hehdt, gpsList[gpsList.length - 1]);
    };

    ws.onclose = function (e) {
        console.log('WebSocket关闭: ');
        console.log(e);
    };

    ws.onerror = function (e) {
        console.log('WebSocket发生错误: ');
        console.log(e);
    };

    // 监听窗口关闭事件，当窗口关闭时，主动去关闭websocket连接，防止连接还没断开就关闭窗口，server端会抛异常。
    window.onbeforeunload = function () {
        ws.close();
    };
}

//心跳检测
let heartCheck = {
    //9分钟发一次心跳
    timeout: 540000,
    timeoutObj: null,
    serverTimeoutObj: null,
    reset: function () {
        clearTimeout(this.timeoutObj);
        clearTimeout(this.serverTimeoutObj);
        return this;
    },
    start: function () {
        let self = this;
        this.timeoutObj = setTimeout(function () {
            //这里发送一个心跳，后端收到后，返回一个心跳消息，
            //onmessage拿到返回的心跳就说明连接正常
            ws.send("ping");
            //如果超过一定时间还没重置，说明后端主动断开了
            //如果onclose会执行reconnect，我们执行ws.close()就行了.如果直接执行reconnect 会触发onclose导致重连两次
            self.serverTimeoutObj = setTimeout(function () {
                ws.close();
            }, self.timeout)
        }, this.timeout)
    }
};

// websocket重新连接
function reconnect(url) {
    if (reconnectLock) {
        return;
    }
    reconnectLock = true;
    setTimeout(function () {
        createWebSocket(url);
        reconnectLock = false;
    }, 3000);
}

function toLesson() {
    let stop = 0;
    //自动缩放
    $("#restore ").click(function () {
        if(stop === 0){
            document.getElementById("restore").title = "开始自动缩放";
            document.getElementById("restore").style =
                " background: url(img/typhoon/start.png); background-repeat: no-repeat; border: none;background-size: 26.8px 26.8px; background-color: #fff; border: 1px solid #ccc;background-size: 95%;background-position: center center;"
            stop = 1;
        }else {
            if (gpsList.length > 2) {
                map.fitBounds(pathPattern.getBounds());
            }
            document.getElementById("restore").title = "暂停自动缩放";
            document.getElementById("restore").style =
                " background: url(img/typhoon/stop.png); background-repeat: no-repeat; border: none;background-size: 26.8px 26.8px; background-color: #fff; border: 1px solid #ccc;background-size: 95%;background-position: center center;"
            stop = 0;
        }

    });
}

