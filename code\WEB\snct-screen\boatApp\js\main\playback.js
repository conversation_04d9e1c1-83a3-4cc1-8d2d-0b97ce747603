let hrefArr = window.location.href.split('?');
//摄像头编号
let channelCode = hrefArr[1];
// 是否全屏
let isFull = false;
let imgWidth = 1000;
//动效持续时间
let animationSpeed = 0;
//定时器时间
let pause = 800;
//判断最后一个slide的变量
let currentSlide = 1;
// 定时器
let timeout;
let imgList;
let imgLength;

$(function () {
    // 初始化图片宽度，应对不同分辨率的屏幕
    initWidth();

    getSnapshot();
    // 事件绑定
    bind();
});

// 初始化图片宽度，应对不同分辨率的屏幕
function initWidth() {
    let clientWidth = document.documentElement.clientWidth;
    imgWidth = parseInt(clientWidth * 0.65);
    $(".slider-container").css('width', imgWidth + 'px');
}

// 事件绑定
function bind() {
    isFllScreen();

    let $slider_container = $(".slider-container");
    $slider_container.mouseover(function (e) {
        $('#modalHeard').show();
    });
    $slider_container.mouseout(function (e) {
        $('#modalHeard').hide();
    });

    $("#stop").on('click', stopPlaying);
    $("#play").on('click', startPlaying);
}

// 全屏事件
function handleFullScreen() {
    let element = document.getElementById("full");
    if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen();
    } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen();
    } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen();
    }
}

//退出全屏
function handleExitFullScreen() {
    if (document.exitFullscreen) {
        document.exitFullscreen();
    } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
    } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
    } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
    }
}

//监听window是否全屏，并进行相应的操作,支持esc键退出
function isFllScreen() {
    window.onresize = function () {
        isFull = !!(document.webkitIsFullScreen || document.mozFullScreen ||
            document.msFullscreenElement || document.fullscreenElement
        );
        if (isFull === false) {
            document.getElementById("fullscreen").style.display = "block";
            document.getElementById("exitFullscreen").style.display = "none";
        } else {
            document.getElementById("fullscreen").style.display = "none";
            document.getElementById("exitFullscreen").style.display = "block";
        }
        resetSlider();
    }
}

function resetSlider() {
    imgWidth = window.screen.width;
    if (isFull === false) {
        imgWidth = parseInt(imgWidth * 0.65);
    }
    $(".slider-container").css('width', imgWidth + 'px');
    stopPlaying();
    getSnapshot();
}

function getSnapshotPath(dir, fileName) {
    return dir + '/' + fileName;
}

function getSnapshot() {
    let endTime = Date.parse(new Date());
    let beginTime = endTime - 24 * 60 * 60 * 1000;

    $.ajax({
        type: 'GET',
        url: laputaHost + "/api/snapshot/log/list/" + getCurrentSn() + "/" + channelCode + "/" + beginTime + "/" + endTime,
        dataType: "json",
        cache: false,
        async: false,
        success: function (result) {
            if (result.data === undefined || result.data.length === 0) {
                alert("无历史图片");
                return;
            }
            imgList = result.data;
            imgLength = result.data.length;
            $(".imgSlides").css('width', (result.data.length * imgWidth) + 'px');
            initProgressBar();
            initHtml();
            startPlaying();
            imgList = [];
        }
    });
}

// 进度条初始化
function initProgressBar() {
    $("#progressBar").slider({
        max: imgLength,
        value: 1,
        orientation: "horizontal",
        range: "min",
        animate: true,
        slide: refreshImgSlider
    });
}

function initHtml() {
    $("#imgSlider .imgSlides").css('margin-left', -(currentSlide * imgWidth) + 'px');
    let cameraArr = "";
    for (let i = 0; i < imgList.length; i++) {
        //预加载图片
        let camera = getSnapshotPath(imgList[i].directory, imgList[i].fileName);
        cameraArr += '<li class="imgSlide"><img  src="' + camera + '"></li>';
    }
    $(".imgSlides").html(cameraArr);
    $(".imgSlide").css('width', imgWidth + 'px');
}

// 拖动进度条修改
function refreshImgSlider() {
    currentSlide = $("#progressBar").slider("value");
    $("#imgSlider .imgSlides").css('margin-left', -(currentSlide * imgWidth) + 'px');
}

// 开始播放图片
function startPlaying() {
    $("#play").hide();
    $("#stop").show();

    handlePlaying();
}

// 暂停播放图片
function stopPlaying() {
    $("#stop").hide();
    $("#play").show();
    // clearInterval(interval);
    clearTimeout(timeout);
}

// 播放动作
function handlePlaying() {
    // interval = setInterval(function () {
    //     $("#imgSlider .imgSlides").animate({'margin-left': ('-=' + imgWidth) + 'px'}, animationSpeed, function () {
    //         currentSlide++;
    //
    //         if (currentSlide === imgLength) {
    //             currentSlide = 1;
    //             $('.imgSlides').css('margin-left', 0);
    //             stopPlaying();
    //             getSnapshot();
    //         }
    //
    //         $("#progressBar").slider("value", currentSlide);
    //     });
    // }, pause);


    $("#imgSlider .imgSlides").animate({'margin-left': ('-=' + imgWidth) + 'px'}, animationSpeed, function () {
        currentSlide++;
        $("#progressBar").slider("value", currentSlide);
    });

    if (currentSlide >= imgLength) {
        currentSlide = 1;
        $('.imgSlides').css('margin-left', 0);
        stopPlaying();
        getSnapshot();
    } else {
        keepPlaying();
    }
}

// 持续播放
function keepPlaying() {
    timeout = setTimeout(handlePlaying, pause);
}










