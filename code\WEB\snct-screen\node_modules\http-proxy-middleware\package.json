{"name": "http-proxy-middleware", "version": "1.3.1", "description": "The one-liner node.js proxy middleware for connect, express and browser-sync", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"clean": "rm -rf dist && rm -rf coverage", "lint": "yarn prettier && yarn eslint", "lint:fix": "yarn prettier:fix && yarn eslint:fix", "eslint": "eslint '{src,test}/**/*.ts'", "eslint:fix": "yarn eslint --fix", "prettier": "prettier --list-different \"**/*.{js,ts,md,yml,json,html}\"", "prettier:fix": "prettier --write \"**/*.{js,ts,md,yml,json,html}\"", "prebuild": "yarn clean", "build": "tsc", "pretest": "yarn build", "test": "jest", "coverage": "jest --coverage --coverageReporters=lcov", "prepare": "yarn build && rm dist/tsconfig.tsbuildinfo"}, "repository": {"type": "git", "url": "https://github.com/chimurai/http-proxy-middleware.git"}, "keywords": ["reverse", "proxy", "middleware", "http", "https", "connect", "express", "fastify", "polka", "browser-sync", "gulp", "grunt-contrib-connect", "websocket", "ws", "cors"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/chimurai/http-proxy-middleware/issues"}, "homepage": "https://github.com/chimurai/http-proxy-middleware#readme", "devDependencies": {"@commitlint/cli": "^12.0.1", "@commitlint/config-conventional": "^12.0.1", "@types/express": "4.17.7", "@types/is-glob": "^4.0.1", "@types/jest": "^26.0.22", "@types/micromatch": "^4.0.1", "@types/node": "^14.14.37", "@types/supertest": "^2.0.10", "@types/ws": "^7.4.0", "@typescript-eslint/eslint-plugin": "^4.19.0", "@typescript-eslint/parser": "^4.19.0", "body-parser": "^1.19.0", "browser-sync": "^2.26.14", "connect": "^3.7.0", "eslint": "^7.23.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^3.3.1", "express": "^4.17.1", "husky": "^4.3.0", "jest": "^26.6.3", "lint-staged": "^10.5.4", "mockttp": "^1.2.0", "open": "^7.4.2", "prettier": "^2.2.1", "supertest": "^6.1.3", "ts-jest": "^26.5.4", "typescript": "^4.2.3", "ws": "^7.4.4"}, "dependencies": {"@types/http-proxy": "^1.17.5", "http-proxy": "^1.18.1", "is-glob": "^4.0.1", "is-plain-obj": "^3.0.0", "micromatch": "^4.0.2"}, "engines": {"node": ">=8.0.0"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "lint-staged"}}, "commitlint": {"extends": ["@commitlint/config-conventional"]}}