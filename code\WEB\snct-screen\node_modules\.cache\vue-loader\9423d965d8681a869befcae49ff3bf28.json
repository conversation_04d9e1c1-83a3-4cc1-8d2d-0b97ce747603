{"remainingRequest": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\indexs\\center-map.vue?vue&type=style&index=0&id=10948d28&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\indexs\\center-map.vue", "mtime": 1754276426821}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753075296083}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753075298360}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753075296703}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1753075295543}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753075297856}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouY2VudGVybWFwIHsNCiAgLm1hcHRpdGxlIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGp1c3RpZnktY29udGVudDogcmlnaHQ7DQogICAgbWFyZ2luOiAyNXB4IDAgNXB4IDA7DQogICAgYm94LXNpemluZzogYm9yZGVyLWJveDsNCg0KICAgIC50aXRsZXRleHQgew0KICAgICAgZm9udC1zaXplOiAyNnB4Ow0KICAgICAgZm9udC13ZWlnaHQ6IDMwMDsNCiAgICAgIGxldHRlci1zcGFjaW5nOiAxcHg7DQogICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoDQogICAgICAgIDkyZGVnLA0KICAgICAgICAjMDA3MmZmIDAlLA0KICAgICAgICAjMDBlYWZmIDQ4Ljg1MjUzOTA2MjUlLA0KICAgICAgICAjMDFhYWZmIDEwMCUNCiAgICAgICk7DQogICAgICAtd2Via2l0LWJhY2tncm91bmQtY2xpcDogdGV4dDsNCiAgICAgIC13ZWJraXQtdGV4dC1maWxsLWNvbG9yOiB0cmFuc3BhcmVudDsNCiAgICAgIG1hcmdpbjogMCAxNnB4Ow0KICAgIH0NCg0KICAgIC56dW8sDQogICAgLnlvdSB7DQogICAgICBiYWNrZ3JvdW5kLXNpemU6IDEwMCUgMTAwJTsNCiAgICAgIHdpZHRoOiAyNnB4Ow0KICAgICAgaGVpZ2h0OiAxNnB4Ow0KICAgICAgbWFyZ2luLXRvcDogN3B4Ow0KICAgIH0NCg0KICAgIC56dW8gew0KICAgICAgYmFja2dyb3VuZDogdXJsKCIuLi8uLi9hc3NldHMvaW1nL3hpZXp1by5wbmciKSBuby1yZXBlYXQ7DQogICAgfQ0KDQogICAgLnlvdSB7DQogICAgICBiYWNrZ3JvdW5kOiB1cmwoIi4uLy4uL2Fzc2V0cy9pbWcveGlleW91LnBuZyIpIG5vLXJlcGVhdDsNCiAgICB9DQogIH0NCg0KICAubWFwd3JhcCB7DQogICAgaGVpZ2h0OiA4NTBweDsNCiAgICB3aWR0aDogMTQxNXB4Ow0KICAgIC8vIHBhZGRpbmc6IDEwcHg7DQogICAgYm94LXNpemluZzogYm9yZGVyLWJveDsNCiAgICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["center-map.vue"], "names": [], "mappings": ";AAyGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "center-map.vue", "sourceRoot": "src/views/indexs", "sourcesContent": ["<!--\r\n * @Author: daidai\r\n * @Date: 2022-03-01 11:17:39\r\n * @LastEditors: Please set LastEditors\r\n * @LastEditTime: 2022-09-29 15:50:18\r\n * @FilePath: \\web-pc\\src\\pages\\big-screen\\view\\indexs\\center-map.vue\r\n-->\r\n<template>\r\n  <div class=\"centermap\">\r\n    <div class=\"maptitle\">\r\n      <div class=\"zuo\"></div>\r\n      <span class=\"titletext\">{{ maptitle }}</span>\r\n      <div class=\"you\"></div>\r\n    </div>\r\n    <div class=\"mapwrap\">\r\n      <dv-border-box-12 style=\"padding: 15px;\">\r\n        <BigMap\r\n          mapId=\"CenterMap\"\r\n          width=\"100%\"\r\n          height=\"100%\"\r\n          :center=\"[120.0, 30.0]\"\r\n          :zoom=\"7\"\r\n          :showControls=\"true\"\r\n          ref=\"CenterMap\"\r\n          @map-ready=\"onMapReady\"\r\n        />\r\n      </dv-border-box-12>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport BigMap from \"../../components/map/BigMap.vue\";\r\nimport { dataModule } from '@/utils/webSocket';\r\nexport default {\r\n  components: {\r\n    BigMap\r\n  },\r\n  data() {\r\n    return {\r\n      maptitle: \"企业数量： 家 | 接入船总数量： 艘\",\r\n      wsCheckTimer: null, // WebSocket数据监听定时器\r\n      shipTotalCount: 0, // 船舶总数量\r\n      enterpriseTotalCount: 0, // 企业总数量\r\n      map: null, // BigMap 实例\r\n    };\r\n  },\r\n  created() {},\r\n\r\n  mounted() {\r\n    this.startDataMonitoring();\r\n  },\r\n  beforeDestroy() {\r\n    // 清除定时器\r\n    if (this.wsCheckTimer) {\r\n      clearInterval(this.wsCheckTimer);\r\n      this.wsCheckTimer = null;\r\n    }\r\n  },\r\n  methods: {\r\n    // BigMap 地图初始化完成事件\r\n    onMapReady(mapInstance) {\r\n      this.map = mapInstance;\r\n      console.log('BigMap 地图初始化完成');\r\n    },\r\n\r\n    // 开始WebSocket数据监听\r\n    startDataMonitoring() {\r\n      this.wsCheckTimer = setInterval(() => {\r\n        this.checkWebSocketData();\r\n      }, 1000);\r\n    },\r\n\r\n    // 检查WebSocket数据\r\n    checkWebSocketData() {\r\n      if (dataModule.D0A02) {\r\n        const newData = dataModule.D0A02;\r\n        // 检查数据是否有变化\r\n        if (newData.ship_num !== this.shipTotalCount || newData.enterprise_num !== this.enterpriseTotalCount) {\r\n          this.shipTotalCount = newData.ship_num || 0;\r\n          this.enterpriseTotalCount = newData.enterprise_num || 0;\r\n\r\n          // 更新地图标题显示\r\n          this.updateMapTitle();\r\n\r\n          console.log('船舶数量数据更新:', {\r\n            ship_num: this.shipTotalCount,\r\n            enterprise_num: this.enterpriseTotalCount\r\n          });\r\n        }\r\n      }\r\n    },\r\n\r\n    // 更新地图标题\r\n    updateMapTitle() {\r\n      this.maptitle = `企业数量：${this.enterpriseTotalCount} 家 | 接入船总数量：${this.shipTotalCount} 艘`;\r\n    },\r\n\r\n\r\n\r\n\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.centermap {\r\n  .maptitle {\r\n    display: flex;\r\n    justify-content: right;\r\n    margin: 25px 0 5px 0;\r\n    box-sizing: border-box;\r\n\r\n    .titletext {\r\n      font-size: 26px;\r\n      font-weight: 300;\r\n      letter-spacing: 1px;\r\n      background: linear-gradient(\r\n        92deg,\r\n        #0072ff 0%,\r\n        #00eaff 48.8525390625%,\r\n        #01aaff 100%\r\n      );\r\n      -webkit-background-clip: text;\r\n      -webkit-text-fill-color: transparent;\r\n      margin: 0 16px;\r\n    }\r\n\r\n    .zuo,\r\n    .you {\r\n      background-size: 100% 100%;\r\n      width: 26px;\r\n      height: 16px;\r\n      margin-top: 7px;\r\n    }\r\n\r\n    .zuo {\r\n      background: url(\"../../assets/img/xiezuo.png\") no-repeat;\r\n    }\r\n\r\n    .you {\r\n      background: url(\"../../assets/img/xieyou.png\") no-repeat;\r\n    }\r\n  }\r\n\r\n  .mapwrap {\r\n    height: 850px;\r\n    width: 1415px;\r\n    // padding: 10px;\r\n    box-sizing: border-box;\r\n    position: relative;\r\n  }\r\n}\r\n</style>\r\n"]}]}