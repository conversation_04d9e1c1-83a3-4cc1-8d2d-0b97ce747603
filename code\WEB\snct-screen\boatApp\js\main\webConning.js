//绝对风速
let absWindSpeed = 0;
//相对风向
let relWindDirection;
//水深
let depth = 0;
let ch1Array = [];
let lonArray3 = [];
let ea600_time = [];
let endTime = Math.ceil(new Date().getTime());
let startTime = endTime - 2 * 3600 * 1000;
//半圆数值-30~30
let process = 0;
let utcTimer = {
    year: '',
    month: '',
    date: '',
    Hours: '',
    Minutes: '',
    Seconds: ''
};
let locationTimer = {
    year: '',
    month: '',
    date: '',
    Hours: '',
    Minutes: '',
    Seconds: ''
};
//建立的连接
let ws = null;
//是否真正建立连接
let lockReconnect = false;
//断开 重连倒计时
let timeoutNum = null;
//30秒一次心跳
let timeout = 15 * 60 * 1000;
//心跳心跳倒计时
let timeoutObj = null;
//心跳倒计时
let serverTimeoutObj = null;


$(function () {
    //初始化gps
    initGpsData();
    //初始化船头方向
    initHerot();
    initSTW();
    //初始化和风相关的数据
    initWindData();
    //初始化水深数据
    // initEA600();
    //创建websocket连接
    createWebsocket();

    document.getElementById("submenuDiv").onclick = function (event) {
        $(".submenu").toggle();
        event.stopPropagation();
    };
    document.body.onclick = function (event) {
        $(".submenu").hide();
    };
});

//初始化GPS数据,包括经纬度、船速、utc时间
function initGpsData() {
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/device/data/getLatestData",
        data: {
            sn: getCurrentSn(),
            deviceCode: '032A'
        },
        dataType: "json",
        success: function (result) {
            if (result === undefined || result.data === undefined) {
                return;
            }
            let latitude = formatLat(result.data.latitude);
            let longitude = formatLong(result.data.longitude);
            let groundSpeed = changeTwoDecimal_f(result.data.groundRateKm, 1);
            let courseSpeed = changeTwoDecimal_f(result.data.groundRateJ, 2);
            let originalTime = result.data.initialBjTime.replace(/\-/g, "/");
            //传值
            refreshGps(latitude, longitude, courseSpeed, groundSpeed, originalTime);
        },
    });

}

//初始化船头方向
function initHerot() {
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/device/data/getLatestData",
        data: {
            sn: getCurrentSn(),
            deviceCode: '042A'
        },
        dataType: "json",
        async: false,
        success: function (result) {
            let hehdt = changeTwoDecimal_f(result.data.hehdt, 1);
            //传值
            refreshHehdt(hehdt);
        },
    });

}

//
function initSTW() {
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/device/data/getLatestData",
        data: {
            sn: getCurrentSn(),
            deviceCode: '040A'
        },
        dataType: "json",
        async: false,
        success: function (result) {
            let waterSpeed = changeTwoDecimal_f(result.data.waterSpeed, 1);
            let mileage = changeTwoDecimal_f(result.data.totalShipMileage, 2);
            //传值
            refreshWaterSpeed(waterSpeed, mileage);
        },
    });
}

//初始化aws信息
function initWindData() {
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/device/data/getLatestData",
        data: {
            sn: getCurrentSn(),
            deviceCode: '046A'
        },
        dataType: "json",
        async: false,
        success: function (result) {
            let absWindSpeed = 0;
            let absWindDirection = 0;
            let relWindSpeed = changeTwoDecimal_f(result.data.relativeWindSpeed, 1);
            let relWindDirection = result.data.relativeWind;
            //传值
            refreshWind(relWindSpeed, relWindDirection, absWindSpeed, absWindDirection);
        },
    });
}

function animateWind(absWindSpeed, relWindDirection, process, hehdt) {
    //绝对风向旋转
    document.getElementById("windDirection").innerText = absWindSpeed;
    document.getElementsByClassName("directiveThat")[0].style.transform = "rotate(" + absWindSpeed + "deg) scale(1.2,1.2)";

    //相对风向旋转
    document.getElementsByClassName("directiveThat2")[0].style.transform = "rotate(" + relWindDirection + "deg) scale(1.2,1.2)";

    //船头移动范围
    animateAngle(process);
    let cell = $(".barKedu_cell").width() / 2 - 1080;
    let pix = cell - hehdt * 3;
    document.getElementById("barKedu_kedu").style.transform = "translateX(" + pix + "px)";

    //船头中间的刻度
    animateScale();

    let t = new Date().getTime() - (1000 * 60 * 60 * 8);
    times();
    times(t);
}

//船头移动范围
function animateAngle(n) {//-30~30
    if (n > 30) {
        n = 30;
    } else if (n < -30) {
        n = -30;
    }
    if (n >= 0) {
        document.getElementsByClassName("centerTopProcess")[0].style.width = (50 / 30) * n + '%';
        document.getElementsByClassName("centerTopProcess")[0].style.left = '50%';
    } else {
        n = n * -1;
        document.getElementsByClassName("centerTopProcess")[0].style.width = (50 / 30) * n + '%';
        document.getElementsByClassName("centerTopProcess")[0].style.right = '50%';
    }
}

function animateScale() {
    let htmlStr = '';
    for (let n = 0; n < 3; n++) {
        htmlStr += '<div class="keduCell on" style="margin-left: 0px;">\n' +
            '                                <p>0</p>\n' +
            '                            </div>';

        for (let j = 1; j < 359; j++) {
            if (j === 0 || j % 10 === 0) {
                htmlStr += '<div class="keduCell on" >';
                htmlStr += '<p>' + j + '</p>';
            } else {
                htmlStr += '<div class="keduCell" >';
            }

            htmlStr += '</div>';
        }
    }
    document.getElementById("barKedu_kedu").innerHTML = htmlStr;
}

//水深曲线
function drawEA600() {
    let myChart = echarts.init(document.getElementById('main'));
    let option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                label: {
                    backgroundColor: '#6a7985'
                }
            }
        },
        grid: {
            left: '1%',
            right: '4%',
            bottom: '3%',
            top: '10%',
            containLabel: true
        },
        xAxis: [{
            axisLabel: {
                color: "#8a9ecf",
                fontSize: '4px'
            },
            axisTick: {
                show: false
            },
            axisLine: {
                show: false
            },
            splitLine: {
                show: false
            },
            type: 'category',
            boundaryGap: false,
            data: lonArray3
        }],
        color: ["#4ff9fa", "#ff9e17"],
        yAxis: [{
            inverse: true,
            type: 'value',
            axisLabel: {
                color: "#4ff9fa",
                fontSize: '4px'
            },
            axisTick: {
                show: false
            },
            axisLine: {
                show: false
            },
            splitLine: {
                show: false
            },
        }],
        series: [{
            name: 'ea600',
            type: 'line',
            areaStyle: {},
            data: ch1Array
        }
        ]
    };
    myChart.setOption(option);
    window.onresize = myChart.resize;
}

//创建websocket
function createWebsocket() {
    let codes = '032A,040A,039A,042A,046A';
    ws = new WebSocket("ws://" + window.location.host + laputaWsHost + "/websocket/" + codes + "/" + getCurrentSn());
    ws.onopen = function (e) {
        console.log('WebSocket已打开: ');
        //开启心跳
        startObj();
    };

    ws.onmessage = function (e) {
        let dataMap = JSON.parse(e.data);
        if (dataMap === undefined) {
            return;
        }

        // if (resData.needRefresh !== undefined && resData.needRefresh !== null && resData.needRefresh === 1) {
        //     let sourceUrl = window.location.href;
        //     let url = window.location.href;
        //     if (sourceUrl.indexOf("?") !== -1) {
        //         url = sourceUrl.split("?")[0];
        //     }
        //     location.href = url + "?ran=" + Math.random();
        // }

        //经纬度、数据时间
        if (dataMap[getCurrentSn() + '_032A'] !== undefined && dataMap[getCurrentSn() + '_032A'] !== null) {
            let gpsData = dataMap[getCurrentSn() + '_032A'];
            let latitude = formatLat(gpsData.latitude);
            let longitude = formatLat(gpsData.longitude);
            //speed over ground
            let groundSpeed = changeTwoDecimal_f(gpsData.groundRateJ, 1);
            //course over ground
            let courseSpeed = changeTwoDecimal_f(gpsData.groundRateKm, 2);
            refreshGps(latitude, longitude, courseSpeed, groundSpeed, gpsData.initialBjTime);
        }

        //风向、风速
        let relWindDirection = 0;
        let absWindSpeed = 0;
        if (dataMap[getCurrentSn() + '_046A'] !== undefined && dataMap[getCurrentSn() + '_046A'] !== null) {
            let windData = dataMap[getCurrentSn() + '_046A'];
            //相对风向
            relWindDirection = windData.relativeWind;
            //相对风速
            let relWindSpeed = changeTwoDecimal_f(windData.relativeWindSpeed, 1);
            let absWindDirection = 0;
            //传值
            refreshWind(relWindSpeed, relWindDirection, absWindSpeed, absWindDirection);
        }

        //船向
        let hehdt = 0;
        if (dataMap[getCurrentSn() + '_042A'] !== undefined && dataMap[getCurrentSn() + '_042A'] !== null) {
            let magData = dataMap[getCurrentSn() + '_042A'];
            hehdt = changeTwoDecimal_f(magData.hehdt, 1);
            //传值
            refreshHehdt(hehdt);
        }

        if (dataMap[getCurrentSn() + '_040A'] !== undefined && dataMap[getCurrentSn() + '_040A'] !== null) {
            let logData = dataMap[getCurrentSn() + '_040A'];
            let waterSpeed = changeTwoDecimal_f(logData.waterSpeed, 1);
            let mileage = changeTwoDecimal_f(logData.totalShipMileage, 2);
            //传值
            refreshWaterSpeed(waterSpeed, mileage);
        }
        // if (resData.ea600Data !== undefined && resData.ea600Data !== null && resData.ea600Data !== "") {
        //     let ea600Data = JSON.parse(resData.ea600Data);
        //     //水深
        //     if (ea600Data.waterDepthM !== "null") {
        //         depth = Math.round(ea600Data.waterDepthM * 100) / 100;
        //         document.getElementById("depth").innerText = depth + 'nm';
        //     }
        //     if (ea600Data.initialTime !== ea600_time[ea600_time.length - 1]) {
        //         ch1Array.shift();
        //         lonArray3.shift();
        //         ea600_time.shift();
        //
        //         ch1Array.push(Math.round(ea600Data.waterDepthM * 100) / 100);
        //         lonArray3.push(ea600Data.initialBjTime.substr(10, 6));
        //         ea600_time.push(ea600Data.initialTime);
        //         drawEA600();
        //     }
        // }
        //船头风向等动态效果
        animateWind(absWindSpeed, relWindDirection, 0, hehdt);

        reset();
    };

    ws.onclose = function (e) {
        console.log('WebSocket关闭: ');
        console.log(e);
        //重连
        reconnect();
    };

    ws.onerror = function (e) {
        console.log('WebSocket发生错误: ');
        console.log(e);
        //重连
        reconnect();
    };
}

//重新连接
function reconnect() {
    if (lockReconnect) {
        return;
    }
    lockReconnect = true;
    //没连接上会一直重连，设置延迟避免请求过多
    timeoutNum && clearTimeout(timeoutNum);
    timeoutNum = setTimeout(function () {
        //新连接
        createWebSocket();
        lockReconnect = false;
    }, 5000);
}

//开启心跳
function startObj() {
    timeoutObj && clearTimeout(timeoutObj);
    serverTimeoutObj && clearTimeout(serverTimeoutObj);
    timeoutObj = setTimeout(function () {
        //这里发送一个心跳，后端收到后，返回一个心跳消息，
        ws.send("heartCheck");
        serverTimeoutObj = setTimeout(function () {
            //超时关闭
            ws.close();
        }, timeout);

    }, timeout)
}

//重置心跳
function reset() {
    //清除时间
    clearTimeout(timeoutObj);
    clearTimeout(serverTimeoutObj);
    //重启心跳
    startObj();
}

//传值
function refreshGps(latitude, longitude, courseSpeed, groundSpeed, originalTime) {
    document.getElementById("lat").innerText = latitude;
    document.getElementById("lon").innerText = longitude;
    document.getElementById("courseSpeed").innerText = courseSpeed + '°';
    document.getElementById("groundSpeed").innerText = groundSpeed + 'kt';

    let time = dataFilter(originalTime);
    let time2 = dataFilter2(originalTime);
    let dataT = getDataTime(originalTime);
    document.getElementById("dataTime").innerText = time;
    document.getElementById("currentT").innerText = dataT;
    document.getElementById("dataTime2").innerText = time2;
    document.getElementById("currentT2").innerText = dataT;
}

function refreshWind(relWindSpeed, relWindDirection, absWindSpeed, absWindDirection) {
    document.getElementById("relWindSpeed").innerText = relWindSpeed + 'm/s';
    document.getElementById("relWindDirection").innerText = relWindDirection + '°';
    document.getElementById("absWindSpeed").innerText = absWindSpeed + 'm/s';
    document.getElementById("absWindDirection").innerText = absWindDirection + '°';
}

function refreshHehdt(hehdt) {
    document.getElementById("herot").innerText = 0 + '°/min';
    document.getElementById("heading").innerText = hehdt + '°';
}

function refreshWaterSpeed(waterSpeed, mileage) {
    document.getElementById("velocity_water").innerText = waterSpeed + 'kn';
    document.getElementById("mileage").innerText = mileage + 'nm';
}

//格式化数据接收时间
function dataFilter(v) {
    if (v) {
        let dd = new Date(v);
        let year = dd.getFullYear();
        let month = dd.getMonth() + 1;
        month = month < 10 ? '0' + month : month;
        let date = dd.getDate();
        date = date < 10 ? '0' + date : date;
        return year + '.' + month + '.' + date;
    } else {
        return v;
    }
}

function dataFilter2(v) {
    if (v) {
        let dd = new Date(v);
        let year = dd.getFullYear();
        let month = dd.getMonth() + 1;
        month = month < 10 ? '0' + month : month;
        let date = dd.getDate();
        date = date < 10 ? '0' + date : date;
        return year + '/' + month + '/' + date + '/';
    } else {
        return v;
    }
}

function getDataTime(v) {
    if (v) {
        let dd = new Date(v);

        let Hours = dd.getHours();
        Hours = Hours < 10 ? '0' + Hours : Hours;
        let Minutes = dd.getMinutes();
        Minutes = Minutes < 10 ? '0' + Minutes : Minutes;

        let Seconds = dd.getSeconds();
        Seconds = Seconds < 10 ? '0' + Seconds : Seconds;

        return Hours + ':' + Minutes + ':' + Seconds
    } else {
        return v
    }
}

function times(v) {
    if (v) {
        let dd = new Date(v);
        utcTimer.year = dd.getFullYear();
        let month = dd.getMonth() + 1;
        utcTimer.month = month < 10 ? '0' + month : month;
        let date = dd.getDate();
        utcTimer.date = date < 10 ? '0' + date : date;
        let Hours = dd.getHours();
        utcTimer.Hours = Hours < 10 ? '0' + Hours : Hours;
        let Minutes = dd.getMinutes();
        utcTimer.Minutes = Minutes < 10 ? '0' + Minutes : Minutes;
        let Seconds = dd.getSeconds();
        utcTimer.Seconds = Seconds < 10 ? '0' + Seconds : Seconds;
    } else {
        let dd = new Date();
        locationTimer.year = dd.getFullYear();
        let month = dd.getMonth() + 1;
        locationTimer.month = month < 10 ? '0' + month : month;
        let date = dd.getDate();
        locationTimer.date = date < 10 ? '0' + date : date;
        let Hours = dd.getHours();
        locationTimer.Hours = Hours < 10 ? '0' + Hours : Hours;
        let Minutes = dd.getMinutes();
        locationTimer.Minutes = Minutes < 10 ? '0' + Minutes : Minutes;
        let Seconds = dd.getSeconds();
        locationTimer.Seconds = Seconds < 10 ? '0' + Seconds : Seconds;
    }
}

