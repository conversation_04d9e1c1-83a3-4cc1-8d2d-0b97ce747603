@font-face {
	font-family: "enBlob";
	src: url('/fonts/AlibabaSans-Bold.otf') format('truetype');
}
@font-face {
	font-family: "enRegular";
	src: url('/fonts/AlibabaSans-Regular.otf') format('truetype');
}
* {
	margin: 0;
	padding: 0px;
	box-sizing: border-box;
}

::-webkit-scrollbar {
	width: 0px;
}

ul li {
	list-style: none;
}

a {
	text-decoration: none;
}

img {
	max-width: 100%;
	display: block;
}

html,
body {
	width: 100%;
	height: 100%;
	background: #020412;
	font-family: "enRegular";
	color: #fff;
}

.flex {
	display: flex;
	display: -webkit-flex;
}

.flex.jlr {
	justify-content: space-between;
}

.flex.j-center {
	justify-content: center;
}

.flex.a-center {
	align-items: center;
}

.flex.col {
	flex-direction: column;
}

.flex.wrap {
	flex-wrap: wrap;
}

.flex.j-end {
	justify-content: flex-end;
}

.flex.a-end {
	align-items: flex-end;
}
.background,
.background img {
	width: 100%;
	position: relative;
}
.main{ position: absolute; left: 0; top: 0; z-index:2; width: 100%; height: 100%;}
.ordBackground{width: 100%; position: absolute; left: 0; top: 0; opacity: 0.3;}
.leftWrap{ width: 30.1vw; height: 100%; padding-left: 3.5%;}
.timer{ font-size: 1.4vw; color: #23cefd; margin-left: -5%; font-weight: 500; margin-top: 3%; margin-bottom: 10.3%;}
.timer h4{ font-weight: 400;}
.timerYearDate{font-size: 0.75vw; text-align: center; padding-left: 5%; padding-top: 2%; line-height: 100%;}
.timerYearDate p{ color: rgba(255,255,255,0.6); white-space: nowrap;}
.timerYearDate span{ padding-top: 3%; display: block;}
.titles{width: 100%; margin-top: 1.2vw; text-indent: 1.2vw; text-align: center; font-size: 1vw; margin-bottom: 0.88vw; line-height: 2.3vw; font-family: "enBlob";}
.boxLine1{ width: 100%; height: 6.8vw; padding-top: 4%; margin-bottom: 3.35vw;}
.boxLine1 .lineItem{ padding: 0px 9%; font-size: 1.8vw; font-weight: 400; line-height: 120%;}
.boxLine1 .lineItem h4{ font-weight: 400; width: 40%;}
.boxLine2{ width: 100%; height: 13.9vw; margin-top: 1.2vw;}
.boxLine2_block{ width: 48.5%; height: 100%;}
.boxLine2_block .kedu{ width: 11vw; position: relative; height: 11vw; background: url(/img/webConning/scale.png) no-repeat; background-size: 100% 100%;}
.leftCard{ width: 100%; height: 100%; transition: all 0.4s; transform-origin: center center; background: url(/img/webConning/leftCar.png) no-repeat; background-size: 100% 100% ;}
.rightCard{ width: 100%; height: 100%; transition: all 0.4s; transform-origin: center center; background: url(/img/webConning/rightCar.png) no-repeat; background-size: 100% 100% ;}
.patchtext{ position: absolute; bottom: 13%; line-height: 120%; color: #fff; width: 100%; font-size: 0.9vw; text-align: center;}
.patchtext span{ font-size: 0.7vw;}
.centerWrap{ flex: 1; height: 100%; padding-top: 9%; padding: 0px 0.9vw; position: relative;}
.centerWrap .Speed{ position: absolute; left: 2%; top: 15%; font-family: "enBlob";}
.centerWrap .Speed.right{ left: auto; right: 2%;}
.centerWrap .Speed.right h2{ text-align: right; vertical-align: top;}
.centerWrap .Speed sup{ font-size: 2vw; line-height: 100%;}
.centerWrap .Speed span{  font-size: 1.15vw; font-weight: 200;}
.centerWrap .Speed h2{ font-size: 3.2vw; line-height: 100%; vertical-align: bottom;}
.centerWrap .Speed h2 em{ font-size: 2vw; font-style: normal;}
.centerTop{ width: 19.739vw; height:5.989vw; margin: auto; margin-top: 36.3%; background: #010e3e; position: relative;}
.centerTopTitle{ font-size: 1vw; position: absolute; left: 50%; transform: translateX(-50%); top: -36%;}
.centerTopTitle1{ font-size: 1vw; position: absolute; z-index: 999; left: 50%; transform: translateX(-50%); top: 84%; text-align: center;}
.centerTopTitle1 h4{ font-size: 1.6vw;}
.centerTopProcess{ position: absolute; transition: all 0.4s; -webkit-transition: all 0.4s; width: 0%; top: 0; height: 90%; background: #0b87e6; z-index: 1;}
.centerTopZZ{background: url(/img/webConning/centerTops.png) no-repeat; background-size: 100% 100%; width: 100%; height: 100%; position: absolute; z-index: 2; left: 0; top: 0;}
.centerHeading{ margin-top: 9.2%; text-align: center;}
.centerHeading h3{ font-size: 2vw;}
.centerHeading .Inputs{ width: 6.25vw; text-align: center; line-height: 3vw; font-size: 1.6vw; height: 3vw; border: 1px solid #fff; margin: auto; margin-top: 1%; }
.rightWrap{width: 30.1vw; height: 100%; padding-right: 3.5%;}
.barKedu{ width:15.3645vw; margin: auto; margin-top: 2.5%; height:2.55vw; background:#1752bf; background-size:100% 100%; position: relative;}
.barKedu_zz{width: 100%; height: 100%; background-image: linear-gradient(to right, rgba(0,0,0,1) , rgba(0,0,0,0.2) , rgba(0,0,0,1)); position: absolute; z-index: 99; left: 0; top: 0;}
.barKedu_kedu{border-top: 1px solid #fff; margin-top: 5px;}
.barKedu_cell{ width: 100%; padding-top: 10px; height: 100%; position: relative; overflow: hidden;}
.barKedu_cell_this{ width: 5px; position: absolute; transform: translateX(-50%); left: 50%; top: 0; height: 100%; transition: all 0.4s; background: url(/img/webConning/this1.png) no-repeat; background-size: 100% 100%;}
.stewBlcok{text-align: center; margin-top: 1%;}
.stewBlcok h3{ font-size: 2vw;}
.stewBlcok .Inputs{ width: 12.25vw; line-height: 3vw; font-size: 1.6vw; height: 3vw; border: 1px solid #fff; margin: auto; margin-top: 0.5%; }
.LeftDuo{ width: 8vw; position: relative; height: 8vw; background: url(/img/webConning/leftDuo.png) no-repeat; background-size: 100% 100%;}
.LeftDuoText{ position: absolute; top: 0; left: 0; width: 100%; text-align: center; font-size: 2vw; line-height: 120%;}
.duoWraps{padding-left:15%; padding-right: 14%; margin-top: -2vw;}
.duoWraps_this{ width: 8vw; height: 8vw; transition: all 0.4s; transform: rotate(0deg); position: absolute; left: 0; top: 0; transform-origin: center center;  background: url(/img/webConning/this2.png); background-size: 100% 100%;}
.directiveBox{width: 100%; height: 19.4vw; margin-top: 1.5vw;}
.directiveBox_item{ width: 48.5%; height: 100%;}
.directiveBox_item h4{ text-align: center; font-size: 0.75vw; margin-top: 3%;}
.directiveBox_item label{ font-size: 0.6vw;}
.directiveBox_item label b{ font-size: 1vw;}
.directiveBox_item .directiveWrap{width:12.1875vw; margin:-0.3vw auto 0 5%; height: 10.5725vw; background: url(/img/webConning/directive.png) no-repeat; background-size: 100% 100%;}
.directivebox,.directiveThat,.directiveText,.directiveThat2,.directiveThat3{ width: 7.0325vw; height: 7.0325vw; position: relative; transition: all 0.4s;}
.directiveThat{transform-origin:center center; transform: scale(1.2,1.2);}

.directiveThat.seep1{ background: url(/img/webConning/windSpeed_01.png) no-repeat; background-size: 100% 100%;}
.directiveThat.seep2{ background: url(/img/webConning/windSpeed_02.png) no-repeat; background-size: 100% 100%;}
.directiveThat.seep3{ background: url(/img/webConning/windSpeed_03.png) no-repeat; background-size: 100% 100%;}
.directiveThat.seep4{ background: url(/img/webConning/windSpeed_04.png) no-repeat; background-size: 100% 100%;}
.directiveThat.seep5{ background: url(/img/webConning/windSpeed_05.png) no-repeat; background-size: 100% 100%;}
.directiveThat.seep6{ background: url(/img/webConning/windSpeed_06.png) no-repeat; background-size: 100% 100%;}
.directiveThat.seep7{ background: url(/img/webConning/windSpeed_07.png) no-repeat; background-size: 100% 100%;}
.directiveThat.seep8{ background: url(/img/webConning/windSpeed_08.png) no-repeat; background-size: 100% 100%;}

.directiveThat2{ background: url(/img/webConning/this4.png) no-repeat; background-size: 100% 100%; transform-origin:center center;}
.directiveThat3{background: url(/img/webConning/this5.png) no-repeat; background-size: 100% 100%; transform-origin:center center; position: absolute; left: 0; top: 0;}
.directiveText{ position: absolute; left: 0; top: 0; text-align: center; line-height: 150%; font-size: 1vw;}
.charLinks{ width: 100%; height: 14vw;}
.mainTitle{ width: 100%; letter-spacing: 2vw; text-align: center; font-size: 3.2vw; color: #78e3ff; position: absolute; left: 0; top: 0;}
.subtns{ width: 5.6vw; cursor: pointer;  position: absolute; z-index:99; left: 28vw; top: 3.5vw;}
.subtns1{ width: 5.6vw; height: 2.1vw; cursor: pointer; position: absolute; z-index:99; right: 28vw; top: 3.5vw;}
.keduCell{width: 1px; height: 4px; background: #fff; float: left; margin-left: 2px;}
.keduCell.on{height: 8px; font-size: 0.1vw; position: relative;}
.keduCell.on p{ font-size: 0.1vw;  position: absolute; transform: translateX(-50%); left: 0; top: 4px;}
.backgroundFooter{width: 100%; height: 2.6vw; text-align: center; color: #0CBFC2; position: absolute; left: 0; bottom: 0;font-size: 1vw}

.submenu{ width: 9.7916vw; text-align: center; background: rgba(255,255,255,0.79); border: 2px solid rgba(12,191,195,0.79); position: absolute; z-index: 99; left: 28vw; top: 6vw;}
.submenu a{ text-align: center; padding-bottom: 2px; background: url(/img/fgx.png) no-repeat center bottom; font-weight: 900; font-size:1vw; text-shadow: 0px 1.3px 1px rgba(0,0,0,0.4); color: #045c5e; line-height: 2vw; display: block; text-stroke:1px #fff; -webkit-text-stroke:0.5px #fff;}
.submenu a:last-child{background: none;}
.subtns{ width: 5.6vw; height: 2.1vw; cursor: pointer; position: absolute; left: 28vw; top: 3.5vw;}
.subtns1{ width: 5.6vw; cursor: pointer; position: absolute; right: 28vw; top: 3.5vw;}
