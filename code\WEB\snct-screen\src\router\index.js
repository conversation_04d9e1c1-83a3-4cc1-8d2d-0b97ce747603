/*
 * @Author: da<PERSON>i
 * @Date: 2022-01-12 14:22:29
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-04-28 14:53:02
 * @FilePath: \web-pc\src\pages\big-screen\router\index.js
 */
import Vue from "vue";
import VueRouter from "vue-router";

Vue.use(VueRouter);

const routes = [
  {
    path: '/login',
    name: 'login',
    component: () => import(/* webpackChunkName: "login" */ '../views/user/login.vue'),
    meta: { title: '登录' }
  },
  {
    path: '/map-test',
    name: 'map-test',
    component: () => import(/* webpackChunkName: "map-test" */ '../views/test/MapTest.vue'),
    meta: { title: '地图测试' }
  },
  {
    path: '/home',
    name: 'home',
    component: () => import(/* webpackChunkName: "LSD.bighome" */ '../views/home.vue'),
    children:[
      {
        path: '/index',
        name: 'index',
        component: () => import(/* webpackChunkName: "LSD.bighome" */ '../views/indexs/index.vue'),
      },
      {
        path: '/comindex',
        name: 'comindex',
        component: () => import(/* webpackChunkName: "LSD.bighome" */ '../views/comindexs/index.vue'),
      }
    ]
  },
];
const router = new VueRouter({
  mode: "history",
  base: process.env.BASE_URL,
  routes
});

// 导航守卫
router.beforeEach((to, from, next) => {
  // 获取token
  const token = localStorage.getItem('Admin-Token')
  const utype = localStorage.getItem('Admin-type')
  // 如果访问登录页面或地图测试页面
  if (to.path === '/login' || to.path === '/' || to.path === '/map-test') {
    console.log(utype+"：utype1:", token);

    // 地图测试页面直接放行
    if (to.path === '/map-test') {
      next()
      return
    }

    // 如果已经登录，跳转到首页
    if (token) {
      if('U01'==utype){
        next('/index')
      }else{
        next('/comindex')
      }
    } else {
      if(to.path === '/'){
        next('/login')
      }else{
        next()
      }
    }
  } else {
    // 访问其他页面，检查是否已登录
    if (token) {
      next()
    } else {
      // 未登录，跳转到登录页
      next('/login')
    }
  }
})

export default router;