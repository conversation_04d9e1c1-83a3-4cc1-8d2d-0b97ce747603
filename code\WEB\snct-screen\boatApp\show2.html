<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8"/>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Cache-Control" content="no-cache, must-revalidate" />
    <meta http-equiv="Expires" content="0" />
    <title>智慧船情</title>
    <script src="js/other/jquery/jquery-3.3.1.min.js"></script>
    <link rel="stylesheet" href="css/main/bootstrap.css">
    <script src="js/bigemap/bootstrap.min.js"></script>
    <link rel="stylesheet" type="text/css" href="css/main/picture.css"/>
    <link rel="stylesheet" type="text/css" href="css/main/swiper.min.css"/>
    <script src="js/common/commonFun.js"></script>
    <script src="js/common/host.js"></script>
    <script src="js/other/axios.min.js"></script>
    <script src="js/other/jquery/jquery.cookie.js"></script>
    <script src="js/common/auth.js"></script>
    <script src="js/other/swiper.min.js"></script>

    <script>
        document.write(
          "<link type='text/css' href='js/hkvs/demo.css?version=" +
            new Date().getTime() +
            "' rel='stylesheet' />"
        );
      </script>
</head>

<body>
<div class="background" id="app" >
    <img src="img/bg/showBg.png" class="backgroundImg">

    <div class="subtns" id="submenuDiv"></div>
    <div class="subtns1" onclick="javascrtpt:window.location.href='index.html'"></div>
    <div class="submenu" style="display: none;z-index: 9999;margin-top: -12px;">
        <!--<a href="webConning.html">船舶信息显示系统</a>-->
        <a href="show2.html">视频监控</a>
        <a href="mapgis.html">实时轨迹</a>
    </div>

    <div class="flex head jlr">
        <!--本地时间-->
        <div class="leftWrap">
            <div class="timer flex a-center">
                <p class="leftYmd"></p>
            </div>
        </div>
        <div class="rightWrap">
            <div class="timer flex a-center"
                 style="margin-left: 0%; padding-top: 0.1vw; justify-content: flex-end; margin-right: -2vw; font-size: 1.2vw;">
                <div class="rightYmd"></div>
            </div>
        </div>
    </div>

    <div class="flex main jlr showImgDiv" style="z-index: 1;">
        
        <div id="divPlugin234" style="width: 66%;height: 78%;margin-left: 110px;margin-top: 33px;z-index: 1;">
        </div>

    </div>

    <div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-body">
                    <div id="modalHeard"
                         style="width:100%;height: 2.5vw;background: rgba(0,16,27,0.7);display: none;position: absolute;">
                        <img class="close" data-dismiss="modal" aria-hidden="true" src="img/picture/close.png"
                             title="关闭"/>
                        <img class="play" src="img/picture/play.png" title="播放历史"/>
                    </div>
                    <img class="camera"/>
                </div>
            </div>

        </div>
    </div>
    <div class="backgroundFooter flex a-center j-center" style="letter-spacing: 0.5vw;">
        Copyright©智慧船情
    </div>

</div>

<script src="js/main/show2.js"></script>






<div style="display: none;" class="left">
      

    <fieldset class="login">
      <legend>登录</legend>
      <table cellpadding="0" cellspacing="3" border="0">
        <tr>
          <td class="tt">IP地址</td>
          <td>
            <input id="loginip" type="text" class="txt" value="**************" />
          </td>
          <!-- <td><input id="loginip" type="text" class="txt" value="************" /></td> -->
          <td class="tt">端口号</td>
          <td><input id="port" type="text" class="txt" value="80" /></td>
        </tr>
        <tr>
          <td class="tt">用户名</td>
          <td>
            <input id="username" type="text" class="txt" value="admin" />
          </td>
          <td class="tt">密码</td>
          <td>
            <input
              id="password"
              type="password"
              class="txt"
              value="snct1122"
            />
          </td>
          <!-- <td><input id="password" type="password" class="txt" value="abcd1234" /></td> -->
        </tr>
        <tr>
          <td class="tt">设备端口</td>
          <td colspan="2">
            <input
              id="deviceport"
              type="text"
              class="txt"
              disabled
            />（可选参数）
          </td>
          <td>
            窗口分割数&nbsp;
            <select class="sel2" onchange="changeWndNum(this.value);">
              <option value="1" selected>1x1</option>
              <option value="2">2x2</option>
              <option value="3">3x3</option>
              <option value="4">4x4</option>
              <option value="1*2">1x2</option>
              <option value="2*1">2x1</option>
            </select>
          </td>
        </tr>
        <tr>
          <td class="tt">RTSP端口</td>
          <td colspan="3">
            <input
              id="rtspport"
              type="text"
              class="txt"
              disabled
            />（可选参数）
          </td>
        </tr>
        <tr>
          <td colspan="4">
            <input
              type="button"
              class="btn2"
              value="登录(http)"
              title="登录http设备"
              onclick="clickLogin(1);"
            />
            <input
              type="button"
              class="btn2"
              value="登录(https)"
              title="登录https设备"
              onclick="clickLogin(2);"
            />
            <input
              type="button"
              class="btn2"
              value="退出"
              onclick="clickLogout();"
            />
            <input
              type="button"
              class="btn2"
              value="获取基本信息"
              onclick="clickGetDeviceInfo();"
            />
          </td>
        </tr>
        <tr>
          <td class="tt">已登录设备</td>
          <td>
            <select
              id="ip"
              class="sel"
              onchange="getChannelInfo();getDevicePort();"
            ></select>
          </td>
          <td class="tt">通道列表</td>
          <td>
            <select id="channels" class="sel"></select>
          </td>
        </tr>
        <tr>
          <td>
            <input
              id="btnStartPlayback"
              type="button"
              class="btn2"
              value="开始预览"
              onclick="clickStartRealPlay();"
            />
          </td>
          <td>
            <input
              type="button"
              class="btn2"
              value="停止预览"
              onclick="clickStopRealPlay();"
            />
          </td>
          <td>
            <input
              type="button"
              class="btn2"
              value="设置文本叠加"
              onclick="setTextOverlay();"
            />
          </td>
          <td>
            <input
              type="button"
              class="btn2"
              value="销毁插件"
              onclick="destroyPlugin();"
            />
          </td>
        </tr>
        <tr>
          <td>
            <input
              type="button"
              class="btn2"
              value="隐藏插件"
              onclick="hidPlugin();"
            />
          </td>
          <td>
            <input
              type="button"
              class="btn2"
              value="展示插件"
              onclick="showPlugin();"
            />
          </td>
          <td>
            <input
              type="button"
              class="btn2"
              value="关闭全部视频播放"
              title="关闭全部视频播放"
              onclick="stopAllPlay();"
            />
          </td>
        </tr>
      </table>
    </fieldset>
    <fieldset class="ipchannel">
      <legend>数字通道</legend>
      <table width="100%" cellpadding="0" cellspacing="3" border="0">
        <tr>
          <td>
            <input
              type="button"
              class="btn"
              value="获取数字通道列表"
              onclick="clickGetDigitalChannelInfo();"
            />
          </td>
        </tr>
        <tr>
          <td>
            <div class="digitaltdiv">
              <table
                id="digitalchannellist"
                class="digitalchannellist"
                cellpadding="0"
                cellspacing="0"
                border="0"
              ></table>
            </div>
          </td>
        </tr>
      </table>
    </fieldset>
    <fieldset class="localconfig">
      <legend>本地配置</legend>
      <table cellpadding="0" cellspacing="3" border="0">
        <tr>
          <td class="tt">播放性能</td>
          <td>
            <select id="netsPreach" name="netsPreach" class="sel">
              <option value="0">最短延时</option>
              <option value="1">实时性好</option>
              <option value="2">均衡</option>
              <option value="3">流畅性好</option>
            </select>
          </td>
          <td class="tt">图像尺寸</td>
          <td>
            <select id="wndSize" name="wndSize" class="sel">
              <option value="0">充满</option>
              <option value="1">4:3</option>
              <option value="2">16:9</option>
            </select>
          </td>
        </tr>
        <tr>
          <td class="tt">规则信息</td>
          <td>
            <select id="ivsMode" name="ivsMode" class="sel">
              <option value="0">禁用</option>
              <option value="1">启用</option>
            </select>
          </td>
          <td class="tt">叠加POS信息</td>
          <td>
            <select id="osdPosInfo" name="osdPosInfo" class="sel">
              <option value="0">禁用</option>
              <option value="1">启用</option>
            </select>
          </td>
        </tr>
        <tr>
          <td class="tt">录像文件打包大小</td>
          <td>
            <select id="packSize" name="packSize" class="sel">
              <option value="0">256M</option>
              <option value="1">512M</option>
              <option value="2">1G</option>
            </select>
          </td>
          <td class="tt">协议类型</td>
          <td>
            <select id="protocolType" name="protocolType" class="sel">
              <option value="0">TCP</option>
              <option value="2">UDP</option>
            </select>
          </td>
        </tr>
        <tr>
          <td class="tt">抓图文件格式</td>
          <td>
            <select
              id="captureFileFormat"
              name="captureFileFormat"
              class="sel"
            >
              <option value="0">JPEG</option>
              <option value="1">BMP</option>
            </select>
          </td>
        </tr>
        <tr>
          <td class="tt">码流秘钥</td>
          <td colspan="3">
            <input id="secretKey" type="password" class="txt" />
          </td>
        </tr>
        <tr>
          <td class="tt">录像文件保存路径</td>
          <td colspan="3">
            <input id="recordPath" type="text" class="txt" />&nbsp;<input
              type="button"
              class="btn"
              value="浏览"
              onclick="clickOpenFileDlg('recordPath', 0);"
            />
          </td>
        </tr>
        <tr>
          <td class="tt">回放下载保存路径</td>
          <td colspan="3">
            <input id="downloadPath" type="text" class="txt" />&nbsp;<input
              type="button"
              class="btn"
              value="浏览"
              onclick="clickOpenFileDlg('downloadPath', 0);"
            />
          </td>
        </tr>
        <tr>
          <td class="tt">预览抓图保存路径</td>
          <td colspan="3">
            <input id="previewPicPath" type="text" class="txt" />&nbsp;<input
              type="button"
              class="btn"
              value="浏览"
              onclick="clickOpenFileDlg('previewPicPath', 0);"
            />
          </td>
        </tr>
        <tr>
          <td class="tt">回放抓图保存路径</td>
          <td colspan="3">
            <input id="playbackPicPath" type="text" class="txt" />&nbsp;<input
              type="button"
              class="btn"
              value="浏览"
              onclick="clickOpenFileDlg('playbackPicPath', 0);"
            />
          </td>
        </tr>
        <tr>
          <td class="tt">回放剪辑保存路径</td>
          <td colspan="3">
            <input
              id="playbackFilePath"
              type="text"
              class="txt"
            />&nbsp;<input
              type="button"
              class="btn"
              value="浏览"
              onclick="clickOpenFileDlg('playbackFilePath', 0);"
            />
          </td>
        </tr>
        <tr>
          <td colspan="4">
            <input
              type="button"
              class="btn"
              value="获取"
              onclick="clickGetLocalCfg();"
            />&nbsp;<input
              type="button"
              class="btn"
              value="设置"
              onclick="clickSetLocalCfg();"
            />&nbsp;&nbsp;修改参数后，需要刷新界面后生效。
          </td>
        </tr>
      </table>
    </fieldset>
  </div>
  <div style="display: none;" class="left">
    <fieldset class="preview">
      <legend>预览</legend>
      <table cellpadding="0" cellspacing="3" border="0">
        <tr>
          <td class="tt">码流类型</td>
          <td>
            <select id="streamtype" class="sel">
              <option value="1">主码流</option>
              <option value="2">子码流</option>
              <option value="3">第三码流</option>
              <option value="4">转码码流</option>
            </select>
          </td>
        </tr>
        <tr>
          <td class="tt">音量</td>
          <td>
            <input
              type="text"
              id="volume"
              class="txt"
              value="50"
              maxlength="3"
            />&nbsp;<input
              type="button"
              class="btn"
              value="设置"
              onclick="clickSetVolume();"
            />（范围：0~100）
          </td>
          <td>
            <input
              type="button"
              class="btn"
              value="打开声音"
              onclick="clickOpenSound();"
            />
            <input
              type="button"
              class="btn"
              value="关闭声音"
              onclick="clickCloseSound();"
            />
          </td>
        </tr>
        <tr>
          <td class="tt">对讲通道</td>
          <td>
            <select id="audiochannels" class="sel"></select>
            <input
              type="button"
              class="btn"
              value="获取通道"
              onclick="clickGetAudioInfo();"
            />
          </td>
          <td>
            <input
              type="button"
              class="btn"
              value="开始对讲"
              onclick="clickStartVoiceTalk();"
            />
            <input
              type="button"
              class="btn"
              value="停止对讲"
              onclick="clickStopVoiceTalk();"
            />
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <input
              type="button"
              class="btn"
              value="抓图"
              onclick="clickCapturePic('preview');"
            />
            <input
              type="button"
              class="btn"
              value="抓图上传"
              onclick="clickCapturePicData();"
            />
            <input
              type="button"
              class="btn"
              value="开始录像"
              onclick="clickStartRecord('realplay');"
            />
            <input
              type="button"
              class="btn"
              value="停止录像"
              onclick="clickStopRecord('realplay');"
            />
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <input
              type="button"
              class="btn2"
              value="启用电子放大"
              onclick="clickEnableEZoom();"
            />
            <input
              type="button"
              class="btn2"
              value="禁用电子放大"
              onclick="clickDisableEZoom();"
            />
            <input
              type="button"
              class="btn2"
              value="启用3D放大"
              onclick="clickEnable3DZoom();"
            />
            <input
              type="button"
              class="btn2"
              value="禁用3D放大"
              onclick="clickDisable3DZoom();"
            />
            <input
              id="fullbtn"
              type="button"
              class="btn"
              value="全屏"
              onclick="clickFullScreen();"
            />
          </td>
        </tr>
      </table>
    </fieldset>
    <fieldset class="draw">
      <legend>绘图</legend>
      <table cellpadding="0" cellspacing="3" border="0">
        <tr>
          <td>
            <input
              type="button"
              class="btn"
              value="启用绘制"
              onclick="clickEnableDraw();"
            />
            <input
              type="button"
              class="btn"
              value="禁用绘制"
              onclick="clickDisableDraw();"
            />
          </td>
        </tr>
        <tr>
          <td>
            图形ID：<input id="snapId" type="text" class="txt" /> 名称：<input
              id="snapName"
              type="text"
              class="txt"
            />
          </td>
        </tr>
        <tr>
          <td>
            <input
              type="button"
              class="btn"
              value="添加图形"
              onclick="clickAddSnapPolygon()"
            />
            <input
              type="button"
              class="btn"
              value="删除图形"
              onclick="clickDelSnapPolygon()"
            />
            <input
              type="button"
              class="btn"
              value="获取图形"
              onclick="clickGetSnapPolygon()"
            />
            <input
              type="button"
              class="btn"
              value="设置图形"
              onclick="clickSetSnapPolygon()"
            />
            <input
              type="button"
              class="btn"
              value="清空图形"
              onclick="clickDelAllSnapPolygon()"
            />
          </td>
        </tr>
      </table>
    </fieldset>
    <fieldset class="ptz">
      <legend>云台控制</legend>
      <table cellpadding="0" cellspacing="3" border="0" class="left">
        <tr>
          <td>
            <input
              type="button"
              class="btn"
              value="左上"
              onmousedown="mouseDownPTZControl(5);"
              onmouseup="mouseUpPTZControl();"
            />
            <input
              type="button"
              class="btn"
              value="上"
              onmousedown="mouseDownPTZControl(1);"
              onmouseup="mouseUpPTZControl();"
            />
            <input
              type="button"
              class="btn"
              value="右上"
              onmousedown="mouseDownPTZControl(7);"
              onmouseup="mouseUpPTZControl();"
            />
          </td>
        </tr>
        <tr>
          <td>
            <input
              type="button"
              class="btn"
              value="左"
              onmousedown="mouseDownPTZControl(3);"
              onmouseup="mouseUpPTZControl();"
            />
            <input
              type="button"
              class="btn"
              value="自动"
              onclick="mouseDownPTZControl(9);"
            />
            <input
              type="button"
              class="btn"
              value="右"
              onmousedown="mouseDownPTZControl(4);"
              onmouseup="mouseUpPTZControl();"
            />
          </td>
        </tr>
        <tr>
          <td>
            <input
              type="button"
              class="btn"
              value="左下"
              onmousedown="mouseDownPTZControl(6);"
              onmouseup="mouseUpPTZControl();"
            />
            <input
              type="button"
              class="btn"
              value="下"
              onmousedown="mouseDownPTZControl(2);"
              onmouseup="mouseUpPTZControl();"
            />
            <input
              type="button"
              class="btn"
              value="右下"
              onmousedown="mouseDownPTZControl(8);"
              onmouseup="mouseUpPTZControl();"
            />
          </td>
        </tr>
      </table>
      <table cellpadding="0" cellspacing="3" border="0" class="left">
        <tr>
          <td class="tt">云台速度</td>
          <td>
            <select id="ptzspeed" class="sel">
              <option value="1">1</option>
              <option value="2">2</option>
              <option value="3">3</option>
              <option value="4" selected>4</option>
              <option value="5">5</option>
              <option value="6">6</option>
              <option value="7">7</option>
            </select>
          </td>
        </tr>
        <tr>
          <td class="tt">预置点号</td>
          <td><input id="preset" type="text" class="txt" value="1" /></td>
        </tr>
        <tr>
          <td colspan="2">
            <input
              type="button"
              class="btn"
              value="设置"
              onclick="clickSetPreset();"
            />
            <input
              type="button"
              class="btn"
              value="调用"
              onclick="clickGoPreset();"
            />
          </td>
        </tr>
      </table>
      <table cellpadding="0" cellspacing="3" border="0" class="left">
        <tr>
          <td class="tt">
            <input
              type="button"
              class="btn2"
              value="变倍+"
              onmousedown="PTZZoomIn()"
              onmouseup="PTZZoomStop()"
            />
          </td>
          <td>
            <input
              type="button"
              class="btn2"
              value="变倍-"
              onmousedown="PTZZoomout()"
              onmouseup="PTZZoomStop()"
            />
          </td>
        </tr>
        <tr>
          <td class="tt">
            <input
              type="button"
              class="btn2"
              value="变焦+"
              onmousedown="PTZFocusIn()"
              onmouseup="PTZFoucusStop()"
            />
          </td>
          <td>
            <input
              type="button"
              class="btn2"
              value="变焦-"
              onmousedown="PTZFoucusOut()"
              onmouseup="PTZFoucusStop()"
            />
          </td>
        </tr>
        <tr>
          <td class="tt">
            <input
              type="button"
              class="btn2"
              value="光圈+"
              onmousedown="PTZIrisIn()"
              onmouseup="PTZIrisStop()"
            />
          </td>
          <td>
            <input
              type="button"
              class="btn2"
              value="光圈-"
              onmousedown="PTZIrisOut()"
              onmouseup="PTZIrisStop()"
            />
          </td>
        </tr>
      </table>
    </fieldset>
    <fieldset class="playback">
      <legend>回放</legend>
      <table width="100%" cellpadding="0" cellspacing="3" border="0">
        <tr>
          <td class="tt">码流类型</td>
          <td>
            <select id="record_streamtype" class="sel">
              <option value="1">主码流</option>
              <option value="2">子码流</option>
            </select>
          </td>
        </tr>
        <tr>
          <td class="tt">开始时间</td>
          <td>
            <input
              id="starttime"
              type="text"
              class="txt"
              value="2013-12-10 00:00:00"
            />（时间格式：2013-11-11 12:34:56）
          </td>
        </tr>
        <tr>
          <td class="tt">结束时间</td>
          <td>
            <input
              id="endtime"
              type="text"
              class="txt"
              value="2013-12-11 23:59:59"
            />
            <input
              type="button"
              class="btn"
              value="搜索"
              onclick="clickRecordSearch(0);"
            />
            <input
              type="button"
              class="btn"
              value="停止下载"
              onclick="clickStopDownload();"
            />
          </td>
        </tr>
        <tr>
          <td class="tt">按时间下载开始时间</td>
          <td>
            <input
              id="downloadstarttime"
              type="text"
              class="txt"
              value="2013-12-10 00:00:00"
            />（时间格式：2013-11-11 12:34:56）
          </td>
        </tr>
        <tr>
          <td class="tt">按时间下载结束时间</td>
          <td>
            <input
              id="downloadendtime"
              type="text"
              class="txt"
              value="2013-12-11 23:59:59"
            />
            <input
              type="button"
              class="btn"
              value="下载"
              onclick="clickStartDownloadRecordByTime();"
            />（相机不支持）
          </td>
        </tr>
        <tr>
          <td colspan="2">
            <div id="searchdiv" class="searchdiv">
              <table
                id="searchlist"
                class="searchlist"
                cellpadding="0"
                cellspacing="0"
                border="0"
              ></table>
            </div>
          </td>
        </tr>
        <tr>
          <td colspan="2">
            <input
              type="button"
              class="btn2"
              value="开始回放"
              onclick="clickStartPlayback();"
            />
            <input
              type="button"
              class="btn2"
              value="停止回放"
              onclick="clickStopPlayback();"
            />
            <input
              id="btnReverse"
              type="button"
              class="btn"
              value="倒放"
              onclick="clickReversePlayback();"
            />
            <input
              type="button"
              class="btn"
              value="单帧"
              onclick="clickFrame();"
            />
            <input
              id="transstream"
              type="checkbox"
              class="vtop"
            />&nbsp;启用转码码流
          </td>
        </tr>
        <tr>
          <td colspan="2">
            <input
              type="button"
              class="btn"
              value="暂停"
              onclick="clickPause();"
            />
            <input
              type="button"
              class="btn"
              value="恢复"
              onclick="clickResume();"
            />
            <input
              type="button"
              class="btn"
              value="慢放"
              onclick="clickPlaySlow();"
            />
            <input
              type="button"
              class="btn"
              value="快放"
              onclick="clickPlayFast();"
            />
          </td>
        </tr>
        <tr>
          <td colspan="2">
            <input
              type="button"
              class="btn"
              value="抓图"
              onclick="clickCapturePic('playback');"
            />
            <input
              type="button"
              class="btn2"
              value="开始剪辑"
              onclick="clickStartRecord('playback');"
            />
            <input
              type="button"
              class="btn2"
              value="停止剪辑"
              onclick="clickStopRecord('playback');"
            />
            <input
              type="button"
              class="btn2"
              value="OSD时间"
              onclick="clickGetOSDTime();"
            />&nbsp;<input id="osdtime" type="text" class="txt" readonly />
          </td>
        </tr>
      </table>
    </fieldset>
    <fieldset class="maintain">
      <legend>系统维护</legend>
      <table width="100%" cellpadding="0" cellspacing="3" border="0">
        <!-- <tr>
                  <td>
                      <input id="edfpassword" type="password">
                      <input type="button" class="btn2" value="导出配置文件" onclick="clickExportDeviceConfig();" />
                  </td>
              </tr> -->
        <tr>
          <td>
            <input
              type="button"
              id="checkVersion"
              class="btn2"
              value="检查插件版本"
              onclick="clickCheckPluginVersion();"
            />
            <input
              type="button"
              class="btn2"
              style="width: 120px"
              value="简单恢复默认参数"
              onclick="clickRestoreDefault();"
            />
            <input
              type="button"
              class="btn2"
              style="width: 120px"
              value="完全恢复默认参数"
              onclick="clickRestoreFullDefault();"
            />
            <input
              type="button"
              class="btn2"
              value="重启设备"
              onclick="restart();"
            />
          </td>
        </tr>

        <!-- <tr>
                  <td>
                      <input id="configFile" type="text" class="txt" />&nbsp;<input type="button" class="btn"
                          value="浏览" onclick="clickOpenFileDlg('configFile', 1);" />&nbsp;<input type="button"
                          class="btn2" value="导入配置文件" onclick="clickImportDeviceConfig();" />
                  </td>
              </tr> -->
        <tr>
          <td>
            <input id="upgradeFile" type="text" class="txt" />&nbsp;<input
              type="button"
              class="btn"
              value="浏览"
              onclick="clickOpenFileDlg('upgradeFile', 1);"
            />&nbsp;<input
              type="button"
              class="btn2"
              value="升级"
              onclick="clickStartUpgrade();"
            />
          </td>
        </tr>
      </table>
    </fieldset>
  </div>
  <div style="display: none;" class="left">
    <fieldset class="operate">
      <legend>操作信息</legend>
      <div id="opinfo" class="opinfo"></div>
    </fieldset>
    <fieldset class="callback">
      <legend>事件回调信息</legend>
      <div id="cbinfo" class="cbinfo"></div>
    </fieldset>
  </div>
</body>

<script src="js/hkvs/jquery-1.7.1.min.js"></script>
<script src="js/hkvs/webVideoCtrl.js"></script>
<script src="js/hkvs/hkvs.js"></script>




<script>

    window.onload = function(){

       //登录视频压缩
        setTimeout(function() {
            clickLogin(1);
            setTimeout(function() {
                var selectElement = document.getElementById('channels');
                var options = selectElement.options;
                for (var i = 0; i < options.length; i++) {
                    clickStartRealPlay(options[i].value,options[i].value-1);
                    //alert(options[i].value); // 或者 options[i].text 来获取文本
                }
                //clickStartRealPlay(1);
            }, 2000);
        }, 2000);

    }

  </script>






</body>

</html>

