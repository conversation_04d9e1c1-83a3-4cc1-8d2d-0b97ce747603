{"remainingRequest": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\indexs\\center-map.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\indexs\\center-map.vue", "mtime": 1754276426821}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753075297856}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgQmlnTWFwIGZyb20gIi4uLy4uL2NvbXBvbmVudHMvbWFwL0JpZ01hcC52dWUiOw0KaW1wb3J0IHsgZGF0YU1vZHVsZSB9IGZyb20gJ0AvdXRpbHMvd2ViU29ja2V0JzsNCmV4cG9ydCBkZWZhdWx0IHsNCiAgY29tcG9uZW50czogew0KICAgIEJpZ01hcA0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBtYXB0aXRsZTogIuS8geS4muaVsOmHj++8miDlrrYgfCDmjqXlhaXoiLnmgLvmlbDph4/vvJog6ImYIiwNCiAgICAgIHdzQ2hlY2tUaW1lcjogbnVsbCwgLy8gV2ViU29ja2V05pWw5o2u55uR5ZCs5a6a5pe25ZmoDQogICAgICBzaGlwVG90YWxDb3VudDogMCwgLy8g6Ii56Ii25oC75pWw6YePDQogICAgICBlbnRlcnByaXNlVG90YWxDb3VudDogMCwgLy8g5LyB5Lia5oC75pWw6YePDQogICAgICBtYXA6IG51bGwsIC8vIEJpZ01hcCDlrp7kvosNCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkge30sDQoNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLnN0YXJ0RGF0YU1vbml0b3JpbmcoKTsNCiAgfSwNCiAgYmVmb3JlRGVzdHJveSgpIHsNCiAgICAvLyDmuIXpmaTlrprml7blmagNCiAgICBpZiAodGhpcy53c0NoZWNrVGltZXIpIHsNCiAgICAgIGNsZWFySW50ZXJ2YWwodGhpcy53c0NoZWNrVGltZXIpOw0KICAgICAgdGhpcy53c0NoZWNrVGltZXIgPSBudWxsOw0KICAgIH0NCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8vIEJpZ01hcCDlnLDlm77liJ3lp4vljJblrozmiJDkuovku7YNCiAgICBvbk1hcFJlYWR5KG1hcEluc3RhbmNlKSB7DQogICAgICB0aGlzLm1hcCA9IG1hcEluc3RhbmNlOw0KICAgICAgY29uc29sZS5sb2coJ0JpZ01hcCDlnLDlm77liJ3lp4vljJblrozmiJAnKTsNCiAgICB9LA0KDQogICAgLy8g5byA5aeLV2ViU29ja2V05pWw5o2u55uR5ZCsDQogICAgc3RhcnREYXRhTW9uaXRvcmluZygpIHsNCiAgICAgIHRoaXMud3NDaGVja1RpbWVyID0gc2V0SW50ZXJ2YWwoKCkgPT4gew0KICAgICAgICB0aGlzLmNoZWNrV2ViU29ja2V0RGF0YSgpOw0KICAgICAgfSwgMTAwMCk7DQogICAgfSwNCg0KICAgIC8vIOajgOafpVdlYlNvY2tldOaVsOaNrg0KICAgIGNoZWNrV2ViU29ja2V0RGF0YSgpIHsNCiAgICAgIGlmIChkYXRhTW9kdWxlLkQwQTAyKSB7DQogICAgICAgIGNvbnN0IG5ld0RhdGEgPSBkYXRhTW9kdWxlLkQwQTAyOw0KICAgICAgICAvLyDmo4Dmn6XmlbDmja7mmK/lkKbmnInlj5jljJYNCiAgICAgICAgaWYgKG5ld0RhdGEuc2hpcF9udW0gIT09IHRoaXMuc2hpcFRvdGFsQ291bnQgfHwgbmV3RGF0YS5lbnRlcnByaXNlX251bSAhPT0gdGhpcy5lbnRlcnByaXNlVG90YWxDb3VudCkgew0KICAgICAgICAgIHRoaXMuc2hpcFRvdGFsQ291bnQgPSBuZXdEYXRhLnNoaXBfbnVtIHx8IDA7DQogICAgICAgICAgdGhpcy5lbnRlcnByaXNlVG90YWxDb3VudCA9IG5ld0RhdGEuZW50ZXJwcmlzZV9udW0gfHwgMDsNCg0KICAgICAgICAgIC8vIOabtOaWsOWcsOWbvuagh+mimOaYvuekug0KICAgICAgICAgIHRoaXMudXBkYXRlTWFwVGl0bGUoKTsNCg0KICAgICAgICAgIGNvbnNvbGUubG9nKCfoiLnoiLbmlbDph4/mlbDmja7mm7TmlrA6Jywgew0KICAgICAgICAgICAgc2hpcF9udW06IHRoaXMuc2hpcFRvdGFsQ291bnQsDQogICAgICAgICAgICBlbnRlcnByaXNlX251bTogdGhpcy5lbnRlcnByaXNlVG90YWxDb3VudA0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOabtOaWsOWcsOWbvuagh+mimA0KICAgIHVwZGF0ZU1hcFRpdGxlKCkgew0KICAgICAgdGhpcy5tYXB0aXRsZSA9IGDkvIHkuJrmlbDph4/vvJoke3RoaXMuZW50ZXJwcmlzZVRvdGFsQ291bnR9IOWutiB8IOaOpeWFpeiIueaAu+aVsOmHj++8miR7dGhpcy5zaGlwVG90YWxDb3VudH0g6ImYYDsNCiAgICB9LA0KDQoNCg0KDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["center-map.vue"], "names": [], "mappings": ";AAgCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;;AAKA;AACA", "file": "center-map.vue", "sourceRoot": "src/views/indexs", "sourcesContent": ["<!--\r\n * @Author: daidai\r\n * @Date: 2022-03-01 11:17:39\r\n * @LastEditors: Please set LastEditors\r\n * @LastEditTime: 2022-09-29 15:50:18\r\n * @FilePath: \\web-pc\\src\\pages\\big-screen\\view\\indexs\\center-map.vue\r\n-->\r\n<template>\r\n  <div class=\"centermap\">\r\n    <div class=\"maptitle\">\r\n      <div class=\"zuo\"></div>\r\n      <span class=\"titletext\">{{ maptitle }}</span>\r\n      <div class=\"you\"></div>\r\n    </div>\r\n    <div class=\"mapwrap\">\r\n      <dv-border-box-12 style=\"padding: 15px;\">\r\n        <BigMap\r\n          mapId=\"CenterMap\"\r\n          width=\"100%\"\r\n          height=\"100%\"\r\n          :center=\"[120.0, 30.0]\"\r\n          :zoom=\"7\"\r\n          :showControls=\"true\"\r\n          ref=\"CenterMap\"\r\n          @map-ready=\"onMapReady\"\r\n        />\r\n      </dv-border-box-12>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport BigMap from \"../../components/map/BigMap.vue\";\r\nimport { dataModule } from '@/utils/webSocket';\r\nexport default {\r\n  components: {\r\n    BigMap\r\n  },\r\n  data() {\r\n    return {\r\n      maptitle: \"企业数量： 家 | 接入船总数量： 艘\",\r\n      wsCheckTimer: null, // WebSocket数据监听定时器\r\n      shipTotalCount: 0, // 船舶总数量\r\n      enterpriseTotalCount: 0, // 企业总数量\r\n      map: null, // BigMap 实例\r\n    };\r\n  },\r\n  created() {},\r\n\r\n  mounted() {\r\n    this.startDataMonitoring();\r\n  },\r\n  beforeDestroy() {\r\n    // 清除定时器\r\n    if (this.wsCheckTimer) {\r\n      clearInterval(this.wsCheckTimer);\r\n      this.wsCheckTimer = null;\r\n    }\r\n  },\r\n  methods: {\r\n    // BigMap 地图初始化完成事件\r\n    onMapReady(mapInstance) {\r\n      this.map = mapInstance;\r\n      console.log('BigMap 地图初始化完成');\r\n    },\r\n\r\n    // 开始WebSocket数据监听\r\n    startDataMonitoring() {\r\n      this.wsCheckTimer = setInterval(() => {\r\n        this.checkWebSocketData();\r\n      }, 1000);\r\n    },\r\n\r\n    // 检查WebSocket数据\r\n    checkWebSocketData() {\r\n      if (dataModule.D0A02) {\r\n        const newData = dataModule.D0A02;\r\n        // 检查数据是否有变化\r\n        if (newData.ship_num !== this.shipTotalCount || newData.enterprise_num !== this.enterpriseTotalCount) {\r\n          this.shipTotalCount = newData.ship_num || 0;\r\n          this.enterpriseTotalCount = newData.enterprise_num || 0;\r\n\r\n          // 更新地图标题显示\r\n          this.updateMapTitle();\r\n\r\n          console.log('船舶数量数据更新:', {\r\n            ship_num: this.shipTotalCount,\r\n            enterprise_num: this.enterpriseTotalCount\r\n          });\r\n        }\r\n      }\r\n    },\r\n\r\n    // 更新地图标题\r\n    updateMapTitle() {\r\n      this.maptitle = `企业数量：${this.enterpriseTotalCount} 家 | 接入船总数量：${this.shipTotalCount} 艘`;\r\n    },\r\n\r\n\r\n\r\n\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.centermap {\r\n  .maptitle {\r\n    display: flex;\r\n    justify-content: right;\r\n    margin: 25px 0 5px 0;\r\n    box-sizing: border-box;\r\n\r\n    .titletext {\r\n      font-size: 26px;\r\n      font-weight: 300;\r\n      letter-spacing: 1px;\r\n      background: linear-gradient(\r\n        92deg,\r\n        #0072ff 0%,\r\n        #00eaff 48.8525390625%,\r\n        #01aaff 100%\r\n      );\r\n      -webkit-background-clip: text;\r\n      -webkit-text-fill-color: transparent;\r\n      margin: 0 16px;\r\n    }\r\n\r\n    .zuo,\r\n    .you {\r\n      background-size: 100% 100%;\r\n      width: 26px;\r\n      height: 16px;\r\n      margin-top: 7px;\r\n    }\r\n\r\n    .zuo {\r\n      background: url(\"../../assets/img/xiezuo.png\") no-repeat;\r\n    }\r\n\r\n    .you {\r\n      background: url(\"../../assets/img/xieyou.png\") no-repeat;\r\n    }\r\n  }\r\n\r\n  .mapwrap {\r\n    height: 850px;\r\n    width: 1415px;\r\n    // padding: 10px;\r\n    box-sizing: border-box;\r\n    position: relative;\r\n  }\r\n}\r\n</style>\r\n"]}]}