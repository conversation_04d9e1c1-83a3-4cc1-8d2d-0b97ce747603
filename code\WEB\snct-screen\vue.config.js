/*
 * @Author: daidai
 * @Date: 2021-11-22 14:57:15
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-04-29 15:12:50
 */
const path = require("path");

function resolve(dir) {
  return path.join(__dirname, dir);
}

const name = process.env.VUE_APP_TITLE || 'SNCT智慧船情大屏系统' // 网页标题

const port = process.env.port || process.env.npm_config_port || 8080 // 端口

module.exports = {
  // 部署生产环境和开发环境下的URL
  publicPath: process.env.NODE_ENV === "production" ? "/" : "/",
  // 在npm run build时，生成文件的目录名称
  outputDir: process.env.VUE_APP_outputDir || 'dist',
  // 用于放置生成的静态资源 (js、css、img、fonts) 的目录
  assetsDir: 'static',
  // 文件名哈希
  filenameHashing: true,
  // 是否开启eslint保存检测
  lintOnSave: process.env.NODE_ENV === 'development',
  // 运行时编译
  runtimeCompiler: false,
  // 需要转译的依赖
  transpileDependencies: [],
  // 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建
  productionSourceMap: false,

  // webpack-dev-server 相关配置
  devServer: {
    host: '0.0.0.0',
    port: port,
    open: true,
    proxy: {
      // detail: https://cli.vuejs.org/config/#devserver-proxy
      [process.env.VUE_APP_BASE_API]: {
        target: `http://localhost:8090/snct-visual`,
        changeOrigin: true,
        pathRewrite: {
          ['^' + process.env.VUE_APP_BASE_API]: ''
        }
      }
    },
    disableHostCheck: true
  },

  css: {
    // 是否使用css分离插件 ExtractTextPlugin
    extract: process.env.NODE_ENV === "production" ? true : false,//是否将组件中的 CSS 提取至一个独立的 CSS 文件中 (而不是动态注入到 JavaScript 中的 inline 代码)。
    sourceMap: false,//是否为 CSS 开启 source map。设置为 true 之后可能会影响构建的性能。
    loaderOptions: {
      sass: {
        prependData: `@import "@/assets/css/variable.scss";`
      }
    },
    requireModuleExtension: true,
  },

  chainWebpack: (config) => {
    // 配置别名
    config.resolve.alias
      .set('@', resolve('src'))
      .set('assets', resolve('src/assets'))
      .set('assetsBig', resolve('src/pages/big-screen/assets'))
      .set('components', resolve('src/components'))
      .set('views', resolve('src/views'))
      .set('api', resolve('src/api'))
      .set('lib', resolve('src/lib'))

    if (process.env.NODE_ENV === "production") {
      // 删除系统默认的splitChunk
      config.optimization.delete("splitChunks");
    }
    // 删除预加载
    //  // 移除 prefetch  插件
    //  config.plugins.delete('prefetch-index')
    //  // 移除 preload 插件
    //  config.plugins.delete('preload-index');
    //   config.optimization.minimizer('terser').tap((args) => {
    //     // 去除生产环境console
    //     args[0].terserOptions.compress.drop_console = true
    //     return args
    //   })
  },
  configureWebpack: {
    name: name,
    resolve: {
      alias: {
        '@': resolve('src')
      }
    },
    output: {
      // 给输出的js名称添加hash
      filename: "static/js/[name].[hash].js",
      chunkFilename: "static/js/[name].[hash].js"
    },
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          // 抽离所有入口的公用资源为一个chunk
          common: {
            name: "chunk-common",
            chunks: "initial",
            minChunks: 2,
            maxInitialRequests: 5,
            minSize: 0,
            priority: 1,
            reuseExistingChunk: true,
            enforce: true
          },
          // 抽离element-ui为单独chunk
          elementUI: {
            name: "chunk-element-ui",
            test: /[\\/]node_modules[\\/]_?element-ui(.*)/,
            chunks: "all",
            priority: 20,
            reuseExistingChunk: true,
            enforce: true
          },
          // 抽离data-view为单独chunk
          datav: {
            name: "chunk-datav",
            test: /[\\/]node_modules[\\/]@jiaminghi[\\/]data-view[\\/]/,
            chunks: "all",
            priority: 15,
            reuseExistingChunk: true,
            enforce: true
          },
          // 抽离第三方库
          libs: {
            name: 'chunk-libs',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: 'initial'
          }
        }
      }
    }
  },
  // 是否为 Babel 或 TypeScript 使用 thread-loader。该选项在系统的 CPU 有多于一个内核时自动启用，仅作用于生产构建。
  parallel: require('os').cpus().length > 1,

  pluginOptions: {
  }
}
