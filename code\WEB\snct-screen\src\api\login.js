import request from '@/utils/request'
// 用户登录
export function login(data) {
  const params = new URLSearchParams()
  Object.keys(data).forEach(key => {
    params.append(key, data[key])
  })
  return request({
    url: '/login',
    method: 'post',
    data: params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 用户退出
export function logout() {
  return request({
    url: '/logout',
    method: 'post'
  })
}

// 获取用户路由权限
export function getRouters() {
  return request({
    url: '/getRouters',
    method: 'get'
  })
}

// 获取用户信息
export function getUserInfo() {
  return request({
    url: '/getUserInfo',
    method: 'get'
  })
}

// 获取验证图片  以及token
export function reqGet(data) {
  return request({
    url: '/system/captcha/get',
    method: 'post',
    data
  })
}

// 滑动或者点选验证
export function reqCheck(data) {
  return request({
    url: '/system/captcha/check',
    method: 'post',
    data
  })
}

// 查询用户个人信息
export function getUserProfile() {
  return request({
    url: '/profile',
    method: 'get'
  })
}