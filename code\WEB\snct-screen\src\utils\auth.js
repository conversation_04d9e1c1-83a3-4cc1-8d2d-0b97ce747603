import cache from '@/utils/cache'

const TokenKey = 'Admin-Token'
const TypeKey = 'Admin-type'

export function getToken() {
  return cache.local.get(TokenKey)
}

export function setToken(token) {
  return cache.local.set(Token<PERSON><PERSON>, token)
}

export function removeToken() {
  cache.local.remove(TypeKey);
  return cache.local.remove(TokenKey)
}

export function getUtype() {
  return cache.local.get(TypeKey)
}

export function setUtype(utype) {
  return cache.local.set(TypeKey, utype)
}

export function removeUtype() {
  cache.local.remove(TypeKey);
  return cache.local.remove(TokenKey)
}
