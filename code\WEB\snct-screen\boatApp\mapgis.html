<html>
<head>
    <title>实时轨迹</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <script src="js/other/jquery/jquery-3.3.1.min.js"></script>
    <script type="text/javascript" src="js/common/host.js"></script>
    <script src="js/common/webSocket.js"></script>
    <link rel="stylesheet" href="plugin/layui/css/layui.css"/>
    <link rel="stylesheet" href="css/bigemap/button.css" rel="stylesheet">
    <link rel="stylesheet" href="css/bigemap/MarkerCluster.Default.css"/>
    <link rel="stylesheet" href="css/bigemap/fselect.css"/>
    <script>
        let now = new Date().getTime();
        document.write('<link href="css/mapCss/style.css?timestamp=' + now + '" rel="stylesheet" type="text/css"/>');
        document.write('<link href="' + mapHost + '/bigemap.js/v2.1.0/bigemap.css" rel="stylesheet" type="text/css"/>');
        document.write('<script src="' + mapHost + '/bigemap.js/v2.1.0/bigemap.js" type="text/javascript" ><\/script>');
    </script>
    <script language="javascript" src="js/bigemap/arc.js"></script>
    <script language="javascript" src="js/common/fselect.js"></script>
    <script type="text/javascript" src="plugin/laydate/laydate.js"></script>
    <script type="text/javascript" src="js/bigemap/bootstrap.min.js"></script>
    <script type="text/javascript" src="js/bigemap/patternUtils.js"></script>
    <script type="text/javascript" src="js/bigemap/arrow/symbol.js"></script>
    <script type="text/javascript" src="js/bigemap/polyline_arrow.js"></script>
    <script type="text/javascript" src="js/bigemap/rotate_marker.js"></script>
    <script type="text/javascript" src="js/bigemap/track/moveMarker.js"></script>
    <script type="text/javascript" src="js/bigemap/bm.geometryutil.js"></script>
    <script src="js/bigemap/bm.markercluster-src.js"></script>
    <link href="css/bigemap/meteorology.css" rel="stylesheet" type="text/css"/>
    <script src="js/other/jquery/jquery.cookie.js"></script>
    <script src="js/common/auth.js"></script>
    <script src="js/common/commonFun.js"></script>
    <script src="js/common/mapCommon.js"></script>
</head>

<body>
<!-- 地图底图 -->
<div id="map" class="my-map" style="height:100%;width:100%;padding: 0 0;cursor:default">
    <div class="backIndex btn_weather div_btn" onclick="javascrtpt:window.location.href='index.html'" title="返回首页"
         style=""></div>
    <div id="restore" class="huifu btn_weather div_btn" title="暂停自动缩放"
         style=" background-size: 95%;border: 1px solid #ccc;"></div>
    <div class="div_btn"><p id="locus" class="locus btn_weather " title="轨迹信息"></p></div>
    <div id="typhoon" class="taifeng btn_weather div_btn" title="台风"
         style=" background-size: 95%;border: 1px solid #ccc;"></div>

    <!--弹出框-->
    <div class="suspend" id="locusFrame">
        <dl>
            <!-- 右部分  -->
            <dd class="suspendQQ"
                style="background:url(img/map/border.png) no-repeat -1.8vw 0;background-size: 20vw 39.5vw;">
                <!-- 地图 热图切换 -->
                <div id="button" style="margin-left: 4.6vw;margin-top: 1.6vw;" data-toggle="buttons">
                    <label class="btn btn-primary btn-xss layui-btn layui-btn-sm" value="satelLite"
                           onclick="changeLayer('satellite')">
                        <input style="display:none;" type="radio" name="options" id="option">卫星图
                    </label>
                    <label class="btn btn-primary btn-xss layui-btn layui-btn-sm" value="satelLite"
                           onclick="changeLayer('sea')">
                        <input style="display:none;" type="radio" name="options" id="option2">海图
                    </label>
                </div>

                <!--实时数据展示-->
                <table style=" margin-top: 0.1vw" id="nowMsg">
                    <tbody>
                    <!-- 航次选择 -->
                    <tr>
                        <td class="textB">船名</td>
                        <td class="textC" id="shipNm">XXX</td>
                    </tr>
                    <tr>
                        <td colspan="2"><img class="imgbg" src="img/map/line.png"></td>
                    </tr>
                    <tr>
                        <td class="textB">航次</td>
                        <td class="textC" id="cruiseCode">KK1908</td>
                    </tr>
                    <tr>
                        <td colspan="2"><img class="imgbg" src="img/map/line.png"></td>
                    </tr>
                    <tr>
                        <td class="textB">经度</td>
                        <td class="textC" id="longitudeValue">0</td>
                    </tr>
                    <tr>
                        <td class="textB">纬度</td>
                        <td class="textC" id="latitudeValue">0</td>
                    </tr>
                    <tr>
                        <td colspan="2"><img class="imgbg" src="img/map/line.png"></td>
                    </tr>
                    <tr>
                        <td class="textB">航速</td>
                        <td class="textC" id="nowGroundRateJ">0.0节</td>
                    </tr>
                    <tr>
                        <td colspan="2"><img class="imgbg" src="img/map/line.png"></td>
                    </tr>
                    <tr>
                        <td class="textB">艏向</td>
                        <td class="textC" id="newAttitude">0</td>
                    </tr>
                    <tr>
                        <td colspan="2"><img class="imgbg" src="img/map/line.png"></td>
                    </tr>
                    <tr>
                        <td rowspan="2" class="textB">数据时间</td>
                        <td class="textC" id="utime1">
                    </tr>
                    <tr>
                        <td class="textC" id="utime2">
                    </tr>
                    </tbody>
                </table>

                <!--历史数据展示-->
                <table style=" margin-top: 0.1vw; display: none" id="hisMsg">
                    <tbody>
                    <!-- 航次选择 -->
                    <tr>
                        <td class="textB">船名</td>
                        <td class="textC" id="shipName"></td>
                    </tr>
                    <tr>
                        <td colspan="2"><img class="imgbg" src="img/map/line.png"></td>
                    </tr>
                    <tr>
                        <td class="textB">航次选择</td>
                        <td class="textC">
                            <select id="shipCruises" lay-verify="required" class="seleship"
                                    onchange="cruiseChange(this)">
                                <optgroup label='请选择' style="color:#9c9c9c;"></optgroup>
                            </select>
                        </td>
                    </tr>

                    <tr>
                        <td colspan="2"><img class="imgbg" src="img/map/line.png"></td>
                    </tr>
                    <tr id="date">
                        <td class="textB ">开始时间</td>
                        <td class="textC">
                            <input type="text" style="color: black" class="inputtime" id="startTime"/>
                        </td>
                    </tr>
                    <tr>
                        <td class="textB ">结束时间</td>
                        <td class="textC">
                            <input type="text" id="endTime" placeholder="结束日期" style="color: black" class="inputtime"/>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2"><img class="imgbg" src="img/map/line.png"></td>
                    </tr>
                    <tr>
                        <td class="textB" style="width: 6vw;">回放操作</td>
                        <td class="textC" style="padding-top: 0.7vw;">
                            <input type="button" value="" title="开始" class="animationButton"
                                   style="background: url(img/map/start.png) center center/2vw 2vw no-repeat;"
                                   id="playButton" onclick="handleAnimation();">
                            <input type="button" value="" title="停止" class="animationButton"
                                   style="background: url(img/map/stop.png) center center/2vw 2vw no-repeat;"
                                   id="stopButton" onclick="stopAnimation(true);">
                            <input type="button" value="" title="减速" class="animationButton"
                                   style="background: url(img/map/back.png) center center/2vw 2vw no-repeat;"
                                   id="slowDownButton" onclick="slowDownAnimation();">
                            <input type="button" value="" title="加速" class="animationButton"
                                   style="background: url(img/map/continue.png) center center/2vw 2vw no-repeat;"
                                   id="accelerateButton" onclick="accelerateAnimation();">
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2"><img class="imgbg" src="img/map/line.png"></td>
                    </tr>
                    <tr>
                        <td class="textB">经度</td>
                        <td class="textC" id="hisLongitude">0</td>
                    </tr>
                    <tr>
                        <td class="textB">纬度</td>
                        <td class="textC" id="hisLatitude">0</td>
                    </tr>
                    <tr>
                        <td colspan="2"><img class="imgbg" src="img/map/line.png"></td>
                    </tr>
                    <tr>
                        <td class="textB">航速</td>
                        <td class="textC" id="hisGroundRateJ">0.0节</td>
                    </tr>
                    <tr>
                        <td colspan="2"><img class="imgbg" src="img/map/line.png"></td>
                    </tr>
                    <tr>
                        <td class="textB">艏向</td>
                        <td class="textC" id="hisHehdt">195°</td>
                    </tr>
                    <tr>
                        <td colspan="2"><img class="imgbg" src="img/map/line.png"></td>
                    </tr>
                    </tbody>
                </table>

                <!-- 查询按钮 -->
                <div id="buttonGroup" class="buttonGroup">
                    <label id="hisData" class="btn btn-primary btn-xss layui-btn ">
                        <a style="color:white;font-size: 0.8vw; ">历史数据</a>
                    </label>
                    <label class="btn btn-primary btn-xss layui-btn ">
                        <a href="index.html" style="color:white;font-size: 0.8vw; ">返回主页</a>
                    </label>
                </div>
            </dd>
        </dl>
    </div>
    <!--台风-->
    <div id="typhoonFrame" style="top:0;width: 0vw;" class="suspend" onmouseout="scrollWheelDisable(false)"
         onmouseover="scrollWheelDisable(true)">
        <img id="closeBox" src="img/typhoon/indent.png" alt=""
             style="position: absolute; top: 56px; right: 285px; box-shadow: -2px 2px 1px #777; background: #777; z-index: 10001;"/>
        <div id="tfBox" class="tf-box active" style="z-index: 99991;position: absolute">
            <div style="margin-bottom: -2px;">
                <ul id="nameList" class="nameList">
                </ul>
            </div>
            <div class="tf-list">
                <div id="tflistHidePanel" class="tf-list-head"
                     style="border: 1px #E4E4E4 solid; width: 273px; border-radius: 4px; display: none;">
                    <img src="img/typhoon/tfbox/fengn.png" alt="" style="float: left;"/>
                    <img id="fengn" src="img/typhoon/switch_off.png" alt="0" align="middle"
                         style="float: right; padding-right: 4px; cursor: pointer;" onclick="openSwitchCamera(this)"/>
                </div>
                <div id="tflist">
                    <table border="0" cellpadding="0" cellspacing="0" style="margin-top: 0;">
                        <thead>
                        <tr>
                            <td style="padding-left: 0;">
                                <img src="img/typhoon/tfbox/fengn.png" alt=""
                                     style="float: left; position: absolute; left: 13px; padding-top: 2px;"/>
                                <img src="img/typhoon/box_table_title.png" style="border: 0;" alt=""/>
                            </td>
                        </tr>
                        </thead>
                    </table>
                    <div style="overflow-x: hidden; height: 193px; margin: auto; width: 278px; border-bottom: 1px #ddd solid; border-right: 1px #ddd solid;">
                        <table border="0" cellpadding="0" cellspacing="0">
                            <tbody id="typhoonPointList" class="layui-table" lay-even lay-skin="nob" lay-size="sm">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!--历史台风查询-->
            <div class="tf-list" style="height: 46px; margin-bottom: 0;" id="divra">
                <div id="03" class="tf-list-head" style="border: 1px #E4E4E4 solid; width: 273px; border-radius: 4px;">
                    <div class="lishi-top">
                        <span>台风列表 </span>
                        <span>
                            <input id="timeYear" style="width: 50px;height: 21px;" type="text" class="layui-input"
                                   autocomplete="off"/>
                        </span>
                        <div id="typhoonListDiv">
                            <select style="width: 67px;height: 21px;" id="typhoonSelect" class="typhoonSelect"
                                    multiple="multiple">
                            </select>

                        </div>
                        <button onclick="queryTyphoonInfo()"
                                style="float: right;font-size: 0.5px;margin-top: -21px;">获取
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--加载地图-->
<script src="js/main/mapgis.js"></script>
<script src="js/typhoon/typhoon.js"></script>
</body>
</html>
