<template>
  <div class="map-controls">
    <!-- 图层切换控件 -->
    <div class="control-group layer-control">
      <div class="control-title">图层</div>
      <div class="control-buttons">
        <el-button
          v-for="layer in availableLayers"
          :key="layer.key"
          :type="currentLayer === layer.key ? 'primary' : 'default'"
          size="mini"
          @click="switchLayer(layer.key)"
        >
          {{ layer.name }}
        </el-button>
      </div>
    </div>

    <!-- 缩放控件 -->
    <div class="control-group zoom-control">
      <div class="control-title">缩放</div>
      <div class="control-buttons">
        <el-button size="mini" icon="el-icon-plus" @click="zoomIn" />
        <el-button size="mini" icon="el-icon-minus" @click="zoomOut" />
        <el-button size="mini" @click="resetView">重置</el-button>
      </div>
    </div>

    <!-- 工具控件 -->
    <div class="control-group tool-control">
      <div class="control-title">工具</div>
      <div class="control-buttons">
        <el-button
          size="mini"
          :type="fullscreen ? 'primary' : 'default'"
          icon="el-icon-full-screen"
          @click="toggleFullscreen"
        >
          {{ fullscreen ? '退出全屏' : '全屏' }}
        </el-button>
        
        <el-button
          size="mini"
          :type="showNineLine ? 'primary' : 'default'"
          @click="toggleNineLine"
        >
          九段线
        </el-button>

        <el-button
          size="mini"
          :type="measureMode ? 'primary' : 'default'"
          @click="toggleMeasure"
        >
          测距
        </el-button>
      </div>
    </div>

    <!-- 显示控件 -->
    <div class="control-group display-control">
      <div class="control-title">显示</div>
      <div class="control-buttons">
        <el-button
          size="mini"
          :type="showCoordinates ? 'primary' : 'default'"
          @click="toggleCoordinates"
        >
          坐标
        </el-button>
        
        <el-button
          size="mini"
          :type="showScale ? 'primary' : 'default'"
          @click="toggleScale"
        >
          比例尺
        </el-button>

        <el-button
          size="mini"
          :type="showOverview ? 'primary' : 'default'"
          @click="toggleOverview"
        >
          鹰眼
        </el-button>
      </div>
    </div>

    <!-- 坐标显示 -->
    <div v-if="showCoordinates" class="coordinate-display">
      <div class="coordinate-item">
        <span>经度: {{ currentCoordinates.lng }}</span>
      </div>
      <div class="coordinate-item">
        <span>纬度: {{ currentCoordinates.lat }}</span>
      </div>
      <div class="coordinate-item">
        <span>缩放: {{ currentZoom }}</span>
      </div>
    </div>

    <!-- 测距结果显示 -->
    <div v-if="measureMode && measureResult" class="measure-result">
      <div class="measure-item">
        <span>距离: {{ measureResult.distance.toFixed(2) }} 海里</span>
      </div>
      <div class="measure-item">
        <span>方位: {{ measureResult.bearing.toFixed(1) }}°</span>
      </div>
      <el-button size="mini" @click="clearMeasure">清除</el-button>
    </div>
  </div>
</template>

<script>
import { mapConfig } from '@/utils/map/mapConfig'
import { coordinateUtils } from '@/utils/map/coordinateUtils'
import { createNineLineLayer, removeNineLineLayer } from '@/utils/map/nineLineData'

export default {
  name: 'MapControls',
  props: {
    // 地图实例
    map: {
      type: Object,
      required: true
    },
    // 当前图层
    currentLayer: {
      type: String,
      default: 'satellite'
    }
  },
  data() {
    return {
      fullscreen: false,
      showNineLine: false,
      showCoordinates: true,
      showScale: true,
      showOverview: false,
      measureMode: false,
      measureResult: null,
      measurePoints: [],
      measureOverlays: [],
      nineLineOverlays: [],
      currentCoordinates: {
        lng: '0.000000°',
        lat: '0.000000°'
      },
      currentZoom: 0,
      availableLayers: [
        { key: 'satellite', name: '卫星图' },
        { key: 'sea', name: '海图' },
        { key: 'street', name: '街道图' }
      ]
    }
  },
  watch: {
    map: {
      handler(newMap) {
        if (newMap) {
          this.bindMapEvents()
          this.updateMapInfo()
        }
      },
      immediate: true
    }
  },
  methods: {
    /**
     * 绑定地图事件
     */
    bindMapEvents() {
      if (!this.map) return

      // 鼠标移动事件
      this.map.on('mousemove', (e) => {
        if (this.showCoordinates) {
          this.updateCoordinates(e.lngLat.lng, e.lngLat.lat)
        }
      })

      // 缩放事件
      this.map.on('zoomend', () => {
        this.updateMapInfo()
      })

      // 移动事件
      this.map.on('moveend', () => {
        this.updateMapInfo()
      })

      // 点击事件（测距模式）
      this.map.on('click', (e) => {
        if (this.measureMode) {
          this.addMeasurePoint(e.lngLat.lng, e.lngLat.lat)
        }
      })
    },

    /**
     * 更新地图信息
     */
    updateMapInfo() {
      if (!this.map) return

      this.currentZoom = this.map.getZoom()
      
      const center = this.map.getCenter()
      this.updateCoordinates(center.lng, center.lat)
    },

    /**
     * 更新坐标显示
     */
    updateCoordinates(lng, lat) {
      const formatted = coordinateUtils.formatCoordinate(lng, lat)
      this.currentCoordinates = {
        lng: formatted.lng,
        lat: formatted.lat
      }
    },

    /**
     * 切换图层
     */
    switchLayer(layerKey) {
      this.$emit('layer-change', layerKey)
    },

    /**
     * 放大
     */
    zoomIn() {
      if (this.map) {
        const currentZoom = this.map.getZoom()
        this.map.setZoom(currentZoom + 1)
      }
    },

    /**
     * 缩小
     */
    zoomOut() {
      if (this.map) {
        const currentZoom = this.map.getZoom()
        this.map.setZoom(currentZoom - 1)
      }
    },

    /**
     * 重置视图
     */
    resetView() {
      if (this.map) {
        const center = new BM.LngLat(mapConfig.defaultCenter[0], mapConfig.defaultCenter[1])
        this.map.setView(center, mapConfig.defaultZoom)
      }
    },

    /**
     * 切换全屏
     */
    toggleFullscreen() {
      this.fullscreen = !this.fullscreen
      this.$emit('fullscreen-change', this.fullscreen)
    },

    /**
     * 切换九段线显示
     */
    toggleNineLine() {
      this.showNineLine = !this.showNineLine
      
      if (this.showNineLine) {
        this.nineLineOverlays = createNineLineLayer(this.map, mapConfig.nineLine)
      } else {
        removeNineLineLayer(this.map, this.nineLineOverlays)
        this.nineLineOverlays = []
      }
    },

    /**
     * 切换坐标显示
     */
    toggleCoordinates() {
      this.showCoordinates = !this.showCoordinates
    },

    /**
     * 切换比例尺显示
     */
    toggleScale() {
      this.showScale = !this.showScale
      this.$emit('scale-change', this.showScale)
    },

    /**
     * 切换鹰眼显示
     */
    toggleOverview() {
      this.showOverview = !this.showOverview
      this.$emit('overview-change', this.showOverview)
    },

    /**
     * 切换测距模式
     */
    toggleMeasure() {
      this.measureMode = !this.measureMode
      
      if (!this.measureMode) {
        this.clearMeasure()
      } else {
        this.$message.info('点击地图上的两个点进行测距')
      }
    },

    /**
     * 添加测距点
     */
    addMeasurePoint(lng, lat) {
      if (!this.measureMode) return

      this.measurePoints.push({ lng, lat })

      // 添加标记
      const marker = new BM.Marker(new BM.LngLat(lng, lat), {
        icon: new BM.Icon({
          url: '/img/markers/measure-point.png',
          size: new BM.Size(16, 16),
          anchor: new BM.Size(8, 8)
        })
      })
      
      this.map.addOverlay(marker)
      this.measureOverlays.push(marker)

      // 如果有两个点，计算距离和方位
      if (this.measurePoints.length === 2) {
        this.calculateMeasureResult()
        this.drawMeasureLine()
      } else if (this.measurePoints.length > 2) {
        // 重新开始测距
        this.clearMeasure()
        this.addMeasurePoint(lng, lat)
      }
    },

    /**
     * 计算测距结果
     */
    calculateMeasureResult() {
      if (this.measurePoints.length < 2) return

      const p1 = this.measurePoints[0]
      const p2 = this.measurePoints[1]

      const distance = coordinateUtils.calculateDistance(p1.lat, p1.lng, p2.lat, p2.lng)
      const bearing = coordinateUtils.calculateBearing(p1.lat, p1.lng, p2.lat, p2.lng)

      this.measureResult = {
        distance,
        bearing,
        points: [p1, p2]
      }
    },

    /**
     * 绘制测距线
     */
    drawMeasureLine() {
      if (this.measurePoints.length < 2) return

      const points = this.measurePoints.map(p => new BM.LngLat(p.lng, p.lat))
      
      const polyline = new BM.Polyline(points, {
        strokeColor: mapConfig.measurement.distance.strokeColor,
        strokeWeight: mapConfig.measurement.distance.strokeWeight,
        strokeOpacity: mapConfig.measurement.distance.strokeOpacity,
        strokeStyle: 'dashed'
      })

      this.map.addOverlay(polyline)
      this.measureOverlays.push(polyline)
    },

    /**
     * 清除测距
     */
    clearMeasure() {
      // 清除覆盖物
      this.measureOverlays.forEach(overlay => {
        this.map.removeOverlay(overlay)
      })
      
      // 重置数据
      this.measurePoints = []
      this.measureOverlays = []
      this.measureResult = null
    }
  },

  beforeDestroy() {
    // 清理资源
    this.clearMeasure()
    removeNineLineLayer(this.map, this.nineLineOverlays)
  }
}
</script>

<style scoped>
.map-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  padding: 15px;
  min-width: 200px;
  max-width: 300px;
}

.control-group {
  margin-bottom: 15px;
}

.control-group:last-child {
  margin-bottom: 0;
}

.control-title {
  font-size: 12px;
  font-weight: bold;
  color: #666;
  margin-bottom: 8px;
  border-bottom: 1px solid #eee;
  padding-bottom: 4px;
}

.control-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.coordinate-display {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px;
  border-radius: 4px;
  font-size: 11px;
  margin-top: 10px;
}

.coordinate-item {
  margin-bottom: 2px;
}

.coordinate-item:last-child {
  margin-bottom: 0;
}

.measure-result {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid #ffc107;
  padding: 8px;
  border-radius: 4px;
  font-size: 11px;
  margin-top: 10px;
}

.measure-item {
  margin-bottom: 4px;
}

.measure-item:last-child {
  margin-bottom: 8px;
}
</style>
