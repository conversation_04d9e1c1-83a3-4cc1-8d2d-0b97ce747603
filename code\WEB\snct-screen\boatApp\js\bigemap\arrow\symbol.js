// import L from 'leaflet';
// enable rotationAngle and rotationOrigin support on BM.Marker
// import 'leaflet-rotatedmarker';

/**
 * Defines several classes of symbol factories,
 * to be used with BM.PolylineDecorator
 */

BM.Symbol = BM.Symbol || {};

/**
 * A simple dash symbol, drawn as a Polyline.
 * Can also be used for dots, if 'pixelSize' option is given the 0 value.
 */
BM.Symbol.Dash = BM.Class.extend({
    options: {
        pixelSize: 10,
        pathOptions: {}
    },

    initialize: function (options) {
        BM.Util.setOptions(this, options);
        this.options.pathOptions.clickable = false;
    },

    buildSymbol: function (dirPoint, latLngs, map, index, total) {
        const opts = this.options;
        const d2r = Math.PI / 180;

        // for a dot, nothing more to compute
        if (opts.pixelSize <= 1) {
            return BM.polyline([dirPoint.latLng, dirPoint.latLng], opts.pathOptions);
        }

        const midPoint = map.project(dirPoint.latLng);
        const angle = (-(dirPoint.heading - 90)) * d2r;
        const a = BM.point(
            midPoint.x + opts.pixelSize * Math.cos(angle + Math.PI) / 2,
            midPoint.y + opts.pixelSize * Math.sin(angle) / 2
        );
        // compute second point by central symmetry to avoid unecessary cos/sin
        const b = midPoint.add(midPoint.subtract(a));
        return BM.polyline([map.unproject(a), map.unproject(b)], opts.pathOptions);
    }
});

BM.Symbol.dash = function (options) {
    return new BM.Symbol.Dash(options);
};

BM.Symbol.ArrowHead = BM.Class.extend({
    options: {
        polygon: true,
        pixelSize: 10,
        headAngle: 60,
        pathOptions: {
            stroke: false,
            weight: 12
        }
    },

    initialize: function (options) {
        BM.Util.setOptions(this, options);
        this.options.pathOptions.clickable = false;
    },

    buildSymbol: function (dirPoint, latLngs, map, index, total) {
        return this.options.polygon
            ? BM.polygon(this._buildArrowPath(dirPoint, map), this.options)
            : BM.polyline(this._buildArrowPath(dirPoint, map), this.options);
    },

    _buildArrowPath: function (dirPoint, map) {
        const d2r = Math.PI / 180;
        const tipPoint = map.project(dirPoint.latLng);
        const direction = (-(dirPoint.heading - 90)) * d2r;
        const radianArrowAngle = this.options.headAngle / 2 * d2r;

        const headAngle1 = direction + radianArrowAngle;
        const headAngle2 = direction - 0;
        const headAngle3 = direction - radianArrowAngle;
        const arrowHead1 = BM.point(
            tipPoint.x - this.options.pixelSize * Math.cos(headAngle1) / 1.5,
            tipPoint.y + this.options.pixelSize * Math.sin(headAngle1) / 1.5);
        const arrowHead2 = BM.point(
            tipPoint.x - this.options.pixelSize * Math.cos(headAngle2) / 2.7,
            tipPoint.y + this.options.pixelSize * Math.sin(headAngle2) / 2.7);
        const arrowHead3 = BM.point(
            tipPoint.x - this.options.pixelSize * Math.cos(headAngle3) / 1.5,
            tipPoint.y + this.options.pixelSize * Math.sin(headAngle3) / 1.5);

        return [
            [map.unproject(arrowHead1),
                dirPoint.latLng,
                map.unproject(arrowHead2)],
            [map.unproject(arrowHead3),
                dirPoint.latLng,
                map.unproject(arrowHead2)]
        ];
    }
});

BM.Symbol.arrowHead = function (options) {
    return new BM.Symbol.ArrowHead(options);
};

BM.Symbol.Marker = BM.Class.extend({
    options: {
        markerOptions: {},
        rotate: false
    },

    initialize: function (options) {
        BM.Util.setOptions(this, options);
        this.options.markerOptions.clickable = false;
        this.options.markerOptions.draggable = false;
    },

    buildSymbol: function (directionPoint, latLngs, map, index, total) {
        if (this.options.rotate) {
            this.options.markerOptions.rotationAngle = directionPoint.heading + (this.options.angleCorrection || 0);
        }
        return BM.marker(directionPoint.latLng, this.options.markerOptions);
    }
});

BM.Symbol.marker = function (options) {
    return new BM.Symbol.Marker(options);
};

BM.Symbol.CircleMarker = BM.Class.extend({
    options: {
        radius: 10,
        pathOptions: {}
    },

    initialize: function (options) {
        BM.Util.setOptions(this, options);
        this.options.pathOptions.clickable = false;
        this.options.pathOptions.draggable = false;
    },

    buildSymbol: function (circlePoint, latLngs, map, index, total) {
        let cm = BM.circleMarker(circlePoint.latLng, this.options);
        cm.on('mouseover', function (e) {
            let style = {
                radius: 4,
                weight: 0.2,
            };
            this.setStyle(style);
            let htmlStr;
            if (circlePoint.pointInfo.length < 5) {
                //没有初始距离
                htmlStr = "<B>船名：</B>" + this.options.shipName + "\<br\>" +
                    "<B>航次：</B>" + this.options.code + "\<br\>" +
                    // "<B>航速：</B>" + circlePoint.pointInfo[3] + "节" + "\<br\>" +
                    "<B>艏向：</B>" + (circlePoint.heading).toFixed(1) + "°\<br\>" +
                    '<B>距初始位置：</B>' + circlePoint.pointInfo[3].toFixed(2) + '海里\<br\>' +
                    "<B>经纬度：</B>" + formatLat(circlePoint.pointInfo[1]) + "," + formatLong(circlePoint.pointInfo[0]) + "\<br\>" +
                    "<B>数据时间：</B>" + timestampToTime(circlePoint.pointInfo[2]);


            } else {
                //有初始距离
                htmlStr = "<B>船名：</B>" + this.options.shipName + "\<br\>" +
                    "<B>航次：</B>" + this.options.code + "\<br\>" +
                    "<B>航速：</B>" + circlePoint.pointInfo[3] + "节" + "\<br\>" +
                    "<B>艏向：</B>" + (circlePoint.heading).toFixed(1) + "°\<br\>" +
                    '<B>距初始位置：</B>' + circlePoint.pointInfo[4].toFixed(2) + '海里\<br\>' +
                    "<B>经纬度：</B>" + formatLat(circlePoint.pointInfo[1]) + "," + formatLong(circlePoint.pointInfo[0]) + "\<br\>" +
                    "<B>数据时间：</B>" + timestampToTime(circlePoint.pointInfo[2]);

            }

            cm.bindTooltip(htmlStr, {sticky: true, autoClose: false}).openTooltip();
        });
        cm.on('mouseout', function (e) {
            let style = {
                radius: 3,
                weight: 1.2,
            };
            this.setStyle(style);

            cm.bindTooltip("").closeTooltip();
        });

        return cm;
    }
});

BM.Symbol.circleMarker = function (options) {
    return new BM.Symbol.CircleMarker(options);
};

// 格式化时间
function timestampToTime(timestamp) {
    var date = new Date(timestamp);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
    var Y = date.getFullYear() + '-';
    var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
    var D = date.getDate() < 10 ? '0' + date.getDate() + ' ' : date.getDate() + ' ';
    var h = date.getHours() < 10 ? '0' + date.getHours() + ':' : date.getHours() + ':';
    var m = date.getMinutes() < 10 ? '0' + date.getMinutes() + ':' : date.getMinutes() + ':';
    var s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
    return Y + M + D + h + m + s;
}
