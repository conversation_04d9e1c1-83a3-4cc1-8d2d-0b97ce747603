const TokenKey = 'BoatApp-Token';

function getToken() {
    return $.cookie(TokenKey);
}

function setToken(token) {
    return $.cookie(TokenKey, token);
}

function removeToken() {
    return $.cookie(TokenKey, '');
}


//发送请求前触发
$.ajaxSetup({
    complete: function (data) {
        if (data.responseJSON === undefined) {
            return;
        }
        if (data.responseJSON.code === 401) {
            ipLogin(window.location.href);
        } else {
            // console.log("已经登录！")
        }
    },
    // 可以设置自定义标头
    beforeSend: function (xhr) {
        // xhr.setRequestHeader('Content-Type', 'application/json;charset=utf-8');
        if (getToken() !== undefined && getToken() !== null && getToken() !== '') {
            xhr.setRequestHeader('Authorization', 'Bearer ' + getToken());
        }
    }
});

function ipLogin(hrefUrl) {
    $.ajax({
        type: "post",
        url: laputaHost + "/login",
        data: {
            username: '',
            password: '',
            type: 2,
            code: ''
        },
        dataType: 'json',
        success: function (data) {
            console.log(data,'iplogin')
            if (data.code === 200) {
                setToken(data.token);
                window.location.href = hrefUrl;
            } else {
                window.location.href = window.location.origin + "/login.html";
            }
        }
    });
}

/**
 * 保存当前选择的sn号
 * @type {string}
 */
const currentSnKey = 'BoatApp-currentSn';

function getCurrentSn() {
    return $.cookie(currentSnKey);
}

function setCurrentSn(sn) {
    return $.cookie(currentSnKey, sn);
}

function removeCurrentSn() {
    return $.cookie(currentSnKey, '');
}

