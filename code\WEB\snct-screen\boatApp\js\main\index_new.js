//第一个里程数
let initLog = 0.0;
let startPoint = [];
//通道号
let channelCode;
//航次开始和结束时间
let startTime;
let endTime;
let shipName;
//航次名称
let cruiseCode;
let hehdt;
//地图
let map;
let zoomLevel = 5;
// 全部的GPS信息
let gpsList = [];
// 全部坐标
let allCoordinate = [];
// 轨迹
let pathPattern;
//小船
let shipMarker;
let moveTime = 0;
let shipImg=new Map();
// 备航中
let inPreparation = false;
//建立的连接
let ws = null;
//是否真正建立连接
let lockReconnect = false;
//30秒一次心跳
let timeout = 30 * 1000;
//心跳心跳倒计时
let timeoutObj = null;
//心跳倒计时
let serverTimeoutObj = null;
let timeoutNum = null;
let count = 0;
let drawColor;
let ci = 0;
let cj = 0;
let colorArr = ['#FFDB6F', '#23FDF0', '#8AAFEC','#FF8955', '#21a40a'];


$(function () {
    drawColor=$.cookie('CurrentColor')
    for(let i in colorArr){
        shipImg.set(colorArr[i],'../img/ship/shipList/position'+i+'.png')
    }
    // loadMenu('index.html');
    shipList();
    //获取航次信息
    getCruise();
    //获取总里程数
    getMile();
    //获取图片
    getSnapshot();
    //放大图片
    toAmplifyImg();
    //连接websocket
    createWebSocket();
    //自动缩放地图
    toLessen();
    bind();

});

function bind() {
    document.getElementById("submenuDiv").onclick = function (event) {
        $(".submenu").toggle();
        event.stopPropagation();
    };
    document.body.onclick = function (event) {
        $(".submenu").hide();
    };

    preventGrid();
}
function shipList() {
    // 获取船只的船名
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/ship/list",
        dataType: "json",
        async: false,
        success: function (result) {
            if (result.data === undefined) {
                return;
            }
            shipList = result.data;

            if (shipList.length <=1) {
                $('#return').hide()
            }
            for (let i in shipList) {
                if ( shipList[i].sn === getCurrentSn()) {
                    shipName = shipList[i].name;
                }
            }
        }
    });

}

//格式化航次时间
function dataTimesFilter(v) {
    if (v) {
        let dd = new Date(v);
        let month = dd.getMonth() + 1;
        month = month < 10 ? '0' + month : month;
        let date = dd.getDate();
        date = date < 10 ? '0' + date : date;
        return month + '月' + date + '日'
    } else {
        return v
    }
}

//经纬度中的日期
function formatDate(value) {
    let date = new Date(value);
    let y = date.getFullYear();
    let MM = date.getMonth() + 1;
    MM = MM < 10 ? ('0' + MM) : MM;
    let d = date.getDate();
    d = d < 10 ? ('0' + d) : d;
    let h = date.getHours();
    h = h < 10 ? ('0' + h) : h;
    let m = date.getMinutes();
    m = m < 10 ? ('0' + m) : m;
    let s = date.getSeconds();
    s = s < 10 ? ('0' + s) : s;
    return y + '-' + MM + '-' + d + ' ' + h + ':' + m + ':' + s;
}

//获得航次信息，包括时间、里程数、航次百分比、航次名称
function getCruise() {
    let timestamp = Date.parse(new Date());
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/shipCruise/queryVoyageInfo/" + getCurrentSn() + '/' + new Date().getTime(),
        dataType: "json",
        async: false,
        success: function (result) {
            //航次时间戳
            startTime = result.startTime;
            endTime = result.finishTime;
            cruiseCode = result.code;
            if(result.shipName != null ){
                shipName = result.shipName;
            }

            syncCruise();
            if ((startTime === undefined || startTime == null)&&(endTime === undefined || endTime == null)){
                if(ci==0){
                    document.getElementById("cruiseCode").innerText = '航次';
                    document.getElementById("date").innerText = '备航';
                    ci++;
                }

             }


            if (timestamp > endTime) {
                inPreparation = true;
                endTime = timestamp;
                document.getElementById("cruiseCode").innerText = '航次';
                document.getElementById("date").innerText = '备航';

            } else {
                inPreparation = false;
                document.getElementById("cruiseCode").innerText = result.code + '航次';
                document.getElementById("date").innerText = dataTimesFilter(startTime) + '-' + dataTimesFilter(endTime);
            }
            if (count === 0) {
                //获取第一个里程数
                getFirstMile();
                //初始化船向
                initHehdt();
                //初始化风向、风速
                initWindData();
                //初始化GPS数据
                initGpsData();
            }
            //绘制CO2图表
            // initAwsChart();
            // 获取地图GPS
            initGpsList();
        }
    });
}

//获取第一个收到的里程数
function getFirstMile() {
    let st = startTime;
    if (inPreparation) {
        st = endTime;
    }
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/device/data/list",
        data: {
            sn: getCurrentSn(),
            deviceCode: '040A',
            startTime: st,
            endTime: Math.round(new Date() / 1000 * 1000),
            interval: 15
        },
        dataType: "json",
        async: false,
        success: function (result) {
            if (result.data === undefined || result.data === null || result.data === '' || result.data.length === 0) {
                return;
            }
            initLog = parseFloat(result.data[0].totalShipMileage);
        }
    });
}

//获取船的总行驶里程
function getMile() {
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/device/data/getLatestData",
        data: {
            sn: getCurrentSn(),
            deviceCode: '040A'
        },
        dataType: "json",
        async: false,
        success: function (result) {
            if (result === undefined || result.data === undefined) {
                return;
            }
            let mileage = changeTwoDecimal_f(result.data.totalShipMileage - initLog, 2);
            syncLog(mileage);
        }
    });
}

//初始化GPS数据,包括经纬度、船速、utc时间
function initGpsData() {
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/device/data/getLatestData",
        data: {
            sn: getCurrentSn(),
            deviceCode: '053A'
        },
        async: false,
        dataType: "json",
        success: function (result) {
            if (result === undefined || result.data === undefined) {
                return;
            }
            let latitude = formatLat(result.data.latitude);
            let longitude = formatLong(result.data.longitude);
            let bjTime = result.data.initialBjTime.replace(/\-/g, "/");

            let utcTime = formatDate(new Date(bjTime) - 8 * 3600 * 1000);
            let groundRate = changeTwoDecimal_f(result.data.groundRate, 2);

            //给元素传值
            syncGps(latitude, longitude, groundRate, utcTime);

            initMap(result.data.latitude, result.data.longitude);
        },
    });
}

//初始化船向
function initHehdt() {
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/device/data/getLatestData",
        data: {
            sn: getCurrentSn(),
            deviceCode: '054A'
        },
        async: false,
        dataType: "json",
        success: function (result) {
            let data = result.data;
            if (data !== undefined && data !== '' && data !== null) {
                hehdt = parseInt(data.hehdt);
            } else {
                hehdt = 90;
            }
            syncHehdt(hehdt);
        },
    });
}

//初始化风向、风速
function initWindData() {
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/device/data/getLatestData",
        data: {
            sn: getCurrentSn(),
            deviceCode: '046A'
        },
        dataType: "json",
        success: function (result) {
            if (result === undefined || result.data === undefined) {
                return;
            }
            let trueWind = result.data.relativeWind;
            let trueWindSpeed = changeTwoDecimal_f(result.data.relativeWindSpeed, 1);
            syncWind(trueWind, trueWindSpeed);
        },
    });

}

//获取图片
function getSnapshot() {
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/snapshot/log/newest/" + getCurrentSn(),
        dataType: "json",
        success: function (result) {
            for (let i = 0; i < 4; i++) {
                if (result.data.length === i) {
                    break;
                }
                $("#img" + i).attr('src', getSnapshotPath(result.data[i].directory, result.data[i].fileName));
                $("#img" + i).attr('data_channelCode', result.data[i].channelCode);
                $("#imgTitle" + i).text(result.data[i].channelName === undefined ? 'XXX' : result.data[i].channelName);
            }
            fillOtherImg(result.data.length);
        },
    });

    refreshSnapshot();
}

function refreshSnapshot() {
    setTimeout(getSnapshot, 60 * 1000);
}

function getSnapshotPath(dir, fileName) {
    return dir + '/' + fileName;
}

function fillOtherImg(len) {
    if (len >= 4) {
        return;
    }
    for (let i = len; i < 4; i++) {
        // $("#img" + i).attr('src', '');
        $("#imgTitle" + i).text('XXX');
    }
}

//点击图片放大
function toAmplifyImg() {
    $(".footItems_cont > img").click(function (event) {
        $(".camera").attr("src", event.target.src);
        $("#myModal").modal({backdrop: 'static', keyboard: false});

        channelCode = event.target.getAttribute('data_channelCode');
        //全屏回放
        toFullScreen();
    });

    $(".modal-body").mousemove(function (e) {
        $('#modalHeard').show();
    });
    $(".modal-body").mouseout(function (e) {
        $('#modalHeard').hide();
    });
}

//全屏查看图片回放
function toFullScreen() {
    $(".play").click(function () {
        let url = "../playback.html?" + channelCode + "?ran=" + Math.random();
        open(url);
    });
}

//开启心跳
function start() {
    timeoutObj && clearTimeout(timeoutObj);
    serverTimeoutObj && clearTimeout(serverTimeoutObj);
    timeoutObj = setTimeout(function () {
        //这里发送一个心跳，后端收到后，返回一个心跳消息，
        ws.send("heartCheck");
        serverTimeoutObj = setTimeout(function () {
            //超时关闭
            ws.close();
        }, timeout);
    }, timeout);
}

//重置心跳
function reset() {
    //清除时间
    clearTimeout(timeoutObj);
    clearTimeout(serverTimeoutObj);
    //重启心跳
    start();
}

//重新连接
function reconnect() {
    if (lockReconnect) {
        return;
    }
    lockReconnect = true;
    timeoutNum && clearTimeout(timeoutNum);
    timeoutNum = setTimeout(function () {
        //新连接
        createWebSocket();
        lockReconnect = false;
    }, 5000);
}

//连接websocket
function createWebSocket() {
    let codes = '053A,040A,054A,046A';
    ws = new WebSocket("ws://" + window.location.host + laputaWsHost + "/websocket/" + codes + "/" + getCurrentSn());
    ws.onopen = function () {
        console.log('WebSocket已打开: ');
        //开启心跳
        start();
    };

    ws.onmessage = function (e) {
        let dataMap = JSON.parse(e.data);

        handleMessage(dataMap);
        reset();

        let currentTime = Math.round(new Date());
        //判断当前时间与操作时间的间隔，大于30秒则调取轨迹四周边界
        if (document.getElementById("restore").title === "暂停自动缩放") {
            if (map === undefined || pathPattern === undefined) {
                return;
            }
            if (currentTime > (moveTime + 1000 * 40)) {
                map.fitBounds(pathPattern.getBounds());
            }
        }
    };

    ws.onclose = function (e) {
        console.log('WebSocket关闭: ');
        console.log(e);
        //重连
        reconnect();
    };

    ws.onerror = function (e) {
        console.log('WebSocket发生错误: ');
        console.log(e);
        //重连
        reconnect();
    };

    // 监听窗口关闭事件，当窗口关闭时，主动去关闭websocket连接，防止连接还没断开就关闭窗口，server端会抛异常。
    window.onbeforeunload = function () {
        ws.close();
    };

}

function handleMessage(dataMap) {
    if (dataMap === undefined) {
        return;
    }
    //船向
    if (dataMap[getCurrentSn() + '_054A'] !== undefined && dataMap[getCurrentSn() + '_054A'] !== null) {
        let magData = dataMap[getCurrentSn() + '_054A'];
        hehdt = magData.hehdt;
        syncHehdt(parseInt(magData.hehdt));
    }

    // 船gps 经纬度 时间 船速
    if (dataMap[getCurrentSn() + '_053A'] !== undefined && dataMap[getCurrentSn() + '_053A'] !== null) {
        let gpsData = dataMap[getCurrentSn() + '_053A'];
        let latitude = formatLat(gpsData.latitude);
        let longitude = formatLong(gpsData.longitude);
        let bjTime = gpsData.initialBjTime;
        let groundRate = changeTwoDecimal_f(gpsData.groundRate, 2);
        let utcTime = formatDate(new Date(bjTime) - 8 * 3600 * 1000);

        //给元素传值
        syncGps(latitude, longitude, groundRate, utcTime);
        refreshShipMarker(gpsData);
    }

    //风向、风速
    if (dataMap[getCurrentSn() + '_046A'] !== undefined && dataMap[getCurrentSn() + '_046A'] !== null) {
        let windData = dataMap[getCurrentSn() + '_046A'];
        let trueWind = windData.relativeWind;
        let trueWindSpeed = changeTwoDecimal_f(windData.relativeWindSpeed, 1);
        syncWind(trueWind, trueWindSpeed);
    }

    if (dataMap[getCurrentSn() + '_040A'] !== undefined && dataMap[getCurrentSn() + '_040A'] !== null) {
        let logData = dataMap[getCurrentSn() + '_040A'];
        let mileage = changeTwoDecimal_f(logData.totalShipMileage - initLog, 2);
        syncLog(mileage);
    }
    count++;
    if (count >= 60 * 5) {
        getCruise();
        count = 1;
    }
}

function syncCruise() {
    let processLine;
    if (new Date().getTime() - startTime < 0) {
        processLine = 0;
    } else {
        let rate = Math.floor((new Date().getTime() - startTime) / (endTime - startTime) * 10000) / 100;
        processLine = rate > 100 ? 100 : rate;
    }
    document.getElementById("processLine").innerText = processLine + '%';
    document.getElementById("cruiseCode").innerText = cruiseCode + '航次';
    document.getElementById("date").innerText = dataTimesFilter(startTime) + '-' + dataTimesFilter(endTime);
    document.getElementById("shipImg").style.width = (processLine) + '%';
    document.getElementsByClassName("title")[0].innerText = shipName + '执行情况';
}

//将websocket传过来的gps信息放进元素中
function syncGps(latitude, longitude, groundRate, utcTime) {
    document.getElementById("lat").innerText = latitude;
    document.getElementById("lon").innerText = longitude;
    document.getElementById("utcT").innerText = utcTime;
    document.getElementById("shipSpeed").innerText = groundRate + '节';
    document.getElementById("shipS").style.transform = "rotate(" + (groundRate * 130 / 27 * 3 - 130) + "deg)";
}

//将websocket传过来的aws信息放进元素中
function syncWind(trueWind, trueWindSpeed) {
    if(trueWind === undefined){
        trueWind = 0;
    }
    document.getElementById("trueW").innerText = trueWind + '°';
    document.getElementById("trueR").style.transform = "rotate(" + trueWind + "deg)";
    document.getElementById("trueWindS").innerText = trueWindSpeed + '节';
    document.getElementById("wSpeed").style.transform = "rotate(" + (trueWindSpeed * 130 / 27 - 130) + "deg)";
}

//将websocket传过来的compass信息放进元素中
function syncHehdt(hehdt) {
    document.getElementById("hehdt").innerText = hehdt + '°';
    document.getElementsByClassName("directiveThat")[0].style.transform = "rotate(" + hehdt + "deg)";
}

function syncLog(mileage) {
    document.getElementById("mileage").innerText = mileage + 'nm';
}

//初始化地图
function initMap(latitude, longitude) {
    BM.Config.HTTP_URL = mapHost;

    map = BM.map('map', null, {
        crs: BM.CRS.EPSG4326,
        center: [latitude, longitude],
        zoom: zoomLevel,
        zoomControl: true,
        minZoom: 3,
        maxZoom: 13,
        attributionControl: false
    });
    //创建一个谷歌卫星图层 ，具体API详情请参见 ：http://www.bigemap.com/offlinemaps/api/#tilelayer
    google_satellite = BM.tileLayer('bigemap.satellite');
    google_satellite.addTo(map);
    //海图
    // google_sea = BM.tileLayer('bigemap.seaMap');
    // selectType(google_satellite, google_sea);
    // 地图九段线添加
    nineLine("#D3D3D3");
    // 国境线添加
    frontier("#D3D3D3");
    //鼠标对地图的监控
    map.on("moveend", function (e) {
        zoomLevel = map.getZoom();
        moveTime = Math.round(new Date());
    });
}

//地图类型
// function selectType(type) {
//     switch (type) {
//         case 'satelLite':
//             map.remove();
//             map = BM.map('map', null, {
//                 crs: BM.CRS.EPSG4326,
//                 zoom: 5,
//                 zoomControl: true,
//                 minZoom: 3,
//                 maxZoom: 18,
//                 attributionControl: false,
//                 doubleClickZoom: false,
//                 // scrollWheelZoom:false,
//             });
//             switchMap();
//             google_satellite.addTo(map);
//             google_sea.remove(map);
//             break;
//         case 'Sea':
//             google_satellite.remove(map);
//             map.remove();
//             map = BM.map('map', null, {
//                 zoom: 5,
//                 zoomControl: true,
//                 minZoom: 3,
//                 maxZoom: 14,
//                 attributionControl: false,
//                 doubleClickZoom: false,
//                 // scrollWheelZoom:false,
//             });
//             switchMap();
//             google_sea.addTo(map);
//             break;
//     }
//     // 地图九段线添加
//     nineLine("#D3D3D3");
//     // 国境线添加
//     frontier("#D3D3D3");
// }

//地图九段线添加
function nineLine(colorLine) {
    let geoJsonArray = getNineLineJson();
    BM.geoJSON(geoJsonArray, {
        style: function (feature) {
            return {color: colorLine, weight: 1};
        }
    }).addTo(map);
}

//  国境线添加
function frontier(colorLine) {
    let frontierJsonArray = getFrontierJson();
    BM.geoJSON(frontierJsonArray, {
        style: function (feature) {
            return {color: colorLine, weight: 1};
        }
    }).addTo(map);
}

// 获取历史gps数据
function initGpsList() {
    let st = startTime;
    if (inPreparation) {
        st = endTime-30*24*60*60*1000;
    }
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/device/data/list",
        data: {
            sn: getCurrentSn(),
            deviceCode: '053A',
            startTime: st,
            endTime: Math.round(new Date() / 1000 * 1000),
            interval: 5
        },
        dataType: "json",
        async: false,
        success: function (result) {
            if (result.data === undefined || result.data === '' || result.data === null || result.data.length===0) {
                return;
            }
            gpsList = eval(result.data);
            getCoordinate();
            drawPathLine();
            drawShipMarker(hehdt, gpsList[gpsList.length - 1]);

            // 初始化起点GPS
            startPoint = [];
            startPoint.push(parseFloat(gpsList[0][1]));
            startPoint.push(parseFloat(gpsList[0][0]));

            gpsList = [];
            allCoordinate = [];
        }
    });
}

//抽取gps中的经纬度
function getCoordinate() {
    if (gpsList === undefined || gpsList.length === 0) {
        return;
    }
    allCoordinate = [];
    let attr;
    for (let i = 0; i < gpsList.length; i++) {
        attr = [];
        attr.push(parseFloat(gpsList[i][1]));
        attr.push(parseFloat(gpsList[i][0]));
        allCoordinate.push(attr);
    }
}

//画轨迹
function drawPathLine() {
    if (gpsList === undefined || gpsList.length === 0 || allCoordinate.length === 0) {
        return;
    }
    if (pathPattern !== undefined) {
        pathPattern.remove();
    }

    //绘线
    pathPattern = BM.polylineDecorator(
        allCoordinate,
        {
            patterns: [
                {
                    offset: 5,
                    repeat: 15,
                    symbol: BM.Symbol.dash({pixelSize: 9, pathOptions: {color: drawColor, weight: 1.5}})
                },
                {
                    offset: 43,
                    repeat: 70,
                    symbol: BM.Symbol.arrowHead({
                        headAngle: 40,
                        pixelSize: 15,
                        color: drawColor,
                        fillOpacity: 1,
                        weight: 1
                    })
                },
                {
                    offset: 0,
                    repeat: 70,
                    symbol: BM.Symbol.circleMarker({
                        radius: 3,
                        color: drawColor,
                        weight: 1.2,
                        fill: true,
                        fillColor: '#effffd',
                        fillOpacity: 1,
                        code: cruiseCode,
                        shipName: shipName
                    })
                },
            ]
        },
        gpsList
    ).addTo(map);
}

// 画船标记
function drawShipMarker(hehdt, gpsArr) {
    if (shipMarker !== undefined) {
        shipMarker.remove();
    }
    shipMarker = BM.marker([gpsArr[1], gpsArr[0]],
        {
            rotationAngle: hehdt,
            icon: BM.icon({
                // 会导致回放船向不再变动
                iconUrl: shipImg.get(drawColor),
                // 大小 x，y
                iconSize: [15,38],
                // 偏移x,y
                iconAnchor: [10, 20],
                popupAnchor: [-3, -76],
            }),

        },
    ).addTo(map);

    shipMarker.on('mouseover', function (e) {
        if (gpsArr.length === 0) {
            return;
        }
        let html = getShipMarkerHtml(hehdt, gpsArr);
        shipMarker.bindTooltip(html).openTooltip();
    });
}

// 船标记html
function getShipMarkerHtml(hehdt, gpsArr) {
    let speed = gpsArr[3];
    let distance = 0;

    if (gpsArr.length >= 5) {
        distance = gpsArr[4].toFixed(2);
    }

    return "<div class='popuoCss'>" +
        "<B>船名：</B>" + shipName + "\<br\>" +
        "<B>航次：</B>" + cruiseCode + "\<br\>" +
        "<B>航速：</B>" + speed + "节" + "\<br\>" +
        "<B>艏向：</B>" + hehdt + "°" + "\<br\>" +
        '<B>距初始位置：</B>' + distance + '海里\<br\>' +
        "<B>经纬度：</B>" + formatLat(gpsArr[1]) + "," + formatLong(gpsArr[0]) + "\<br\>" +
        "<B>数据时间：</B>" + transformTime(gpsArr[2]) + "\<br\></div>";
}

function refreshShipMarker(gpsData) {
    let arr = [];
    arr.push(parseFloat(gpsData.latitude));
    arr.push(parseFloat(gpsData.longitude));

    let tempGps = [];
    tempGps.push(parseFloat(gpsData.longitude).toFixed(7));
    tempGps.push(parseFloat(gpsData.latitude).toFixed(7));
    tempGps.push(parseInt(gpsData.initialTime));
    if (gpsData.groundRate !== null && gpsData.groundRate !== undefined) {
        tempGps.push(gpsData.groundRate);
    } else {
        tempGps.push(0);
    }
    // 加上离起点距离
    if (map === undefined) {
        //暂时不做处理
        initMap(gpsData.latitude,gpsData.longitude);
        startPoint.push(parseFloat(gpsData.latitude));
        startPoint.push(parseFloat(gpsData.longitude));
    }else{
        if(startPoint.length==0 && cj ===0){
            startPoint.push(parseFloat(gpsData.latitude));
            startPoint.push(parseFloat(gpsData.longitude));
            cj++;
        }
        let dis = (map.distance(startPoint, arr)) / 1852;
        tempGps.push(dis);
        drawShipMarker(hehdt, tempGps);
    }

}

//自动缩放地图
function toLessen() {
    let stop = 0;
    $("#restore").click(function () {
        if (stop === 0) {
            document.getElementById("restore").title = "开始自动缩放";
            document.getElementById("restore").style =
                " background: url(img/typhoon/start.png); background-repeat: no-repeat; border: none;background-size: 26.8px 26.8px; background-color: #fff; border: 1px solid #ccc;background-size: 95%;background-position: center center;"
            stop = 1;
        } else {

            map.fitBounds(pathPattern.getBounds());

            document.getElementById("restore").title = "暂停自动缩放";
            document.getElementById("restore").style =
                " background: url(img/typhoon/stop.png); background-repeat: no-repeat; border: none;background-size: 26.8px 26.8px; background-color: #fff; border: 1px solid #ccc;background-size: 95%;background-position: center center;"
            stop = 0;
        }
    });
}


