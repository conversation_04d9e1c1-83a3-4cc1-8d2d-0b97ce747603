/**
 * 地图通用混入
 * 提供地图组件的通用方法和生命周期管理
 */

import { mapConfig } from '@/utils/map/mapConfig'
import { mapUtils } from '@/utils/map/mapUtils'
import { coordinateUtils } from '@/utils/map/coordinateUtils'

export const mapMixin = {
  data() {
    return {
      map: null,
      mapLoaded: false,
      mapError: null,
      overlays: [], // 存储地图覆盖物
      layers: new Map(), // 存储图层
      markers: new Map(), // 存储标记
      polylines: new Map(), // 存储折线
      polygons: new Map() // 存储多边形
    }
  },

  computed: {
    /**
     * 地图是否可用
     */
    isMapReady() {
      return this.mapLoaded && this.map && !this.mapError
    },

    /**
     * 地图配置
     */
    mapConfig() {
      return mapConfig
    }
  },

  methods: {
    /**
     * 等待地图库加载
     */
    async waitForMapLibrary() {
      return new Promise((resolve, reject) => {
        if (window.BM) {
          resolve()
          return
        }

        let attempts = 0
        const maxAttempts = 50
        const checkInterval = setInterval(() => {
          attempts++
          if (window.BM) {
            clearInterval(checkInterval)
            resolve()
          } else if (attempts >= maxAttempts) {
            clearInterval(checkInterval)
            reject(new Error('地图库加载超时'))
          }
        }, 100)
      })
    },

    /**
     * 初始化地图
     */
    async initializeMap(containerId, options = {}) {
      try {
        await this.waitForMapLibrary()
        
        this.map = mapUtils.createMap(containerId, options)
        this.bindMapEvents()
        this.mapLoaded = true
        this.mapError = null
        
        this.$emit('map-ready', this.map)
        return this.map
      } catch (error) {
        this.mapError = error
        this.$emit('map-error', error)
        throw error
      }
    },

    /**
     * 绑定地图事件
     */
    bindMapEvents() {
      if (!this.map) return

      // 地图点击事件
      this.map.on('click', (e) => {
        this.onMapClick(e)
      })

      // 地图缩放事件
      this.map.on('zoomend', (e) => {
        this.onMapZoom(e)
      })

      // 地图移动事件
      this.map.on('moveend', (e) => {
        this.onMapMove(e)
      })

      // 地图加载完成事件
      this.map.on('tilesloaded', (e) => {
        this.onMapTilesLoaded(e)
      })
    },

    /**
     * 地图点击事件处理
     */
    onMapClick(e) {
      this.$emit('map-click', e)
    },

    /**
     * 地图缩放事件处理
     */
    onMapZoom(e) {
      this.$emit('map-zoom', e)
    },

    /**
     * 地图移动事件处理
     */
    onMapMove(e) {
      this.$emit('map-move', e)
    },

    /**
     * 地图瓦片加载完成事件处理
     */
    onMapTilesLoaded(e) {
      this.$emit('map-tiles-loaded', e)
    },

    /**
     * 添加覆盖物
     */
    addOverlay(overlay, id = null) {
      if (!this.map || !overlay) return

      this.map.addOverlay(overlay)
      
      if (id) {
        this.overlays.push({ id, overlay })
      } else {
        this.overlays.push({ overlay })
      }

      return overlay
    },

    /**
     * 移除覆盖物
     */
    removeOverlay(overlay) {
      if (!this.map || !overlay) return

      this.map.removeOverlay(overlay)
      
      const index = this.overlays.findIndex(item => item.overlay === overlay)
      if (index > -1) {
        this.overlays.splice(index, 1)
      }
    },

    /**
     * 根据ID移除覆盖物
     */
    removeOverlayById(id) {
      const item = this.overlays.find(item => item.id === id)
      if (item) {
        this.removeOverlay(item.overlay)
      }
    },

    /**
     * 清除所有覆盖物
     */
    clearAllOverlays() {
      this.overlays.forEach(item => {
        if (item.overlay) {
          this.map.removeOverlay(item.overlay)
        }
      })
      this.overlays = []
      this.markers.clear()
      this.polylines.clear()
      this.polygons.clear()
    },

    /**
     * 添加标记
     */
    addMarker(lng, lat, options = {}, id = null) {
      if (!this.isMapReady) return null

      const marker = mapUtils.createMarker(lng, lat, options)
      if (marker) {
        this.addOverlay(marker, id)
        if (id) {
          this.markers.set(id, marker)
        }
      }
      return marker
    },

    /**
     * 移除标记
     */
    removeMarker(id) {
      const marker = this.markers.get(id)
      if (marker) {
        this.removeOverlay(marker)
        this.markers.delete(id)
      }
    },

    /**
     * 获取标记
     */
    getMarker(id) {
      return this.markers.get(id)
    },

    /**
     * 添加折线
     */
    addPolyline(points, options = {}, id = null) {
      if (!this.isMapReady) return null

      const polyline = mapUtils.createPolyline(points, options)
      if (polyline) {
        this.addOverlay(polyline, id)
        if (id) {
          this.polylines.set(id, polyline)
        }
      }
      return polyline
    },

    /**
     * 移除折线
     */
    removePolyline(id) {
      const polyline = this.polylines.get(id)
      if (polyline) {
        this.removeOverlay(polyline)
        this.polylines.delete(id)
      }
    },

    /**
     * 设置地图中心
     */
    setMapCenter(lng, lat, zoom = null) {
      if (!this.isMapReady) return

      const center = new BM.LngLat(lng, lat)
      if (zoom !== null) {
        this.map.setView(center, zoom)
      } else {
        this.map.setCenter(center)
      }
    },

    /**
     * 设置地图缩放级别
     */
    setMapZoom(zoom) {
      if (!this.isMapReady) return

      this.map.setZoom(zoom)
    },

    /**
     * 适应边界
     */
    fitBounds(points, options = {}) {
      if (!this.isMapReady) return

      mapUtils.fitBounds(this.map, points, options)
    },

    /**
     * 获取地图边界
     */
    getMapBounds() {
      if (!this.isMapReady) return null

      return mapUtils.getBounds(this.map)
    },

    /**
     * 获取地图中心点
     */
    getMapCenter() {
      if (!this.isMapReady) return null

      const center = this.map.getCenter()
      return {
        lng: center.lng,
        lat: center.lat
      }
    },

    /**
     * 获取地图缩放级别
     */
    getMapZoom() {
      if (!this.isMapReady) return null

      return this.map.getZoom()
    },

    /**
     * 屏幕坐标转地理坐标
     */
    pixelToLngLat(x, y) {
      if (!this.isMapReady) return null

      return mapUtils.pixelToLngLat(this.map, x, y)
    },

    /**
     * 地理坐标转屏幕坐标
     */
    lngLatToPixel(lng, lat) {
      if (!this.isMapReady) return null

      return mapUtils.lngLatToPixel(this.map, lng, lat)
    },

    /**
     * 计算距离
     */
    calculateDistance(lng1, lat1, lng2, lat2) {
      return coordinateUtils.calculateDistance(lat1, lng1, lat2, lng2)
    },

    /**
     * 计算方位角
     */
    calculateBearing(lng1, lat1, lng2, lat2) {
      return coordinateUtils.calculateBearing(lat1, lng1, lat2, lng2)
    },

    /**
     * 验证坐标
     */
    isValidCoordinate(lng, lat) {
      return coordinateUtils.isValidCoordinate(lng, lat)
    },

    /**
     * 格式化坐标
     */
    formatCoordinate(lng, lat, format = 'decimal') {
      return coordinateUtils.formatCoordinate(lng, lat, format)
    },

    /**
     * 销毁地图
     */
    destroyMap() {
      if (this.map) {
        this.clearAllOverlays()
        this.layers.clear()
        
        if (typeof this.map.destroy === 'function') {
          this.map.destroy()
        }
        
        this.map = null
        this.mapLoaded = false
        this.mapError = null
      }
    }
  },

  beforeDestroy() {
    this.destroyMap()
  }
}

export default mapMixin
