* {
    margin: 0;
    padding: 0px;
    box-sizing: border-box;
}

@font-face {
    font-family: "digit";
    src: url('../../fonts/DS-DIGIT-4.ttf') format('truetype');
}

::-webkit-scrollbar {
    width: 0;
}

ul li {
    list-style: none;
    display: inline-block;
}

a {
    text-decoration: none;
}

img {
    max-width: 100%;
    display: block;
}

html,
body {
    width: 100%;
    height: 100%;
    background: #000;
    font-family: "microsoft yahei", arial;
    color: #fff;
}

.flex {
    display: flex;
    display: -webkit-flex;
}

.flex.jlr {
    justify-content: space-between;
}

.flex.j-center {
    justify-content: center;
}

.flex.a-center {
    align-items: center;
}

.flex.col {
    flex-direction: column;
}

.flex.wrap {
    flex-wrap: wrap;
}

.flex.j-end {
    justify-content: flex-end;
}

.flex.a-end {
    align-items: flex-end;
}

.titleLabel {
    width: 32.08vw;
    height: 4.6875vw;
    position: absolute;
    left: 50%;
    top: 1.6%;
    transform: translateX(-50%);
    letter-spacing: 2vw;
    text-align: center;
    font-size: 3.2vw;
    color: #78e3ff;
    font-weight: 600;
}

.fade {
    opacity: 0;
    -webkit-transition: opacity .15s linear;
    -o-transition: opacity .15s linear;
    transition: opacity .15s linear;
}

.fade.in {
    opacity: 1;
}

button.close {
    -webkit-appearance: none;
    padding: 0;
    cursor: pointer;
    background: transparent;
    border: 0;
}

.modal-open {
    overflow: hidden;
}

.modal {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1050;
    display: none;
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
    outline: 0;
}

.modal.fade .modal-dialog {
    -webkit-transition: -webkit-transform .3s ease-out;
    -o-transition: -o-transform .3s ease-out;
    transition: transform .3s ease-out;
    -webkit-transform: translate(0, -25%);
    -ms-transform: translate(0, -25%);
    -o-transform: translate(0, -25%);
    transform: translate(0, -25%);
}

.modal.in .modal-dialog {
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
}

.modal-open .modal {
    overflow-x: hidden;
    overflow-y: auto;
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: 10px;
}

.modal-content {
    position: relative;
    background-color: #fff;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    border: 1px solid #999;
    border: 1px solid rgba(0, 0, 0, .2);
    border-radius: 6px;
    outline: 0;
    -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, .5);
    box-shadow: 0 3px 9px rgba(0, 0, 0, .5);
}

.modal-backdrop {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1040;
    background-color: #000;
}

.modal-backdrop.fade {
    filter: alpha(opacity=0);
    opacity: 0;
}

.modal-backdrop.in {
    filter: alpha(opacity=50);
    opacity: .5;
}

.modal-header {
    min-height: 16.42857143px;
    padding: 15px;
    border-bottom: 1px solid #e5e5e5;
}

.modal-header .close {
    margin-top: -2px;
}

.modal-title {
    margin: 0;
    line-height: 1.42857143;
}

.modal-body {
    position: relative;
    padding: 15px;
}

.modal-footer {
    padding: 15px;
    text-align: right;
    border-top: 1px solid #e5e5e5;
}

.modal-footer .btn + .btn {
    margin-bottom: 0;
    margin-left: 5px;
}

.modal-footer .btn-group .btn + .btn {
    margin-left: -1px;
}

.modal-footer .btn-block + .btn-block {
    margin-left: 0;
}

.modal-scrollbar-measure {
    position: absolute;
    top: -9999px;
    width: 50px;
    height: 50px;
    overflow: scroll;
}

@media (min-width: 768px) {
    .modal-dialog {
        width: 600px;
        margin: 30px auto;
    }

    .modal-content {
        -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, .5);
        box-shadow: 0 5px 15px rgba(0, 0, 0, .5);
    }

    .modal-sm {
        width: 300px;
    }
}

@media (min-width: 992px) {
    .modal-lg {
        width: 900px;
    }
}

.modal-footer:before,
.modal-footer:after {
    display: table;
    content: " ";
}

#myModal {
    width: 100%;
    height: 100%;
}

.modal-dialog {
    width: 66%;
    height: 100%;
    margin-top: 6%;
}

.modal-body {
    padding: 0;
}

.camera {
    width: 100%;
}

.close {
    width: 1.8vw;
    position: absolute;
    right: 0.5vw;
    top: 0.2vw;
}

.play {
    width: 1.7vw;
    position: absolute;
    right: 2.5vw;
    top: 0.25vw;
}

.close:hover, .play:hover {
    cursor: pointer;
}


.titleLabel img {
    width: 100%;
    height: 100%;
}

.background,
.background img {
    width: 100%;
    position: relative;
}

#map img {
    width: 100%;
    position: inherit;
}

.mainWrap {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 2;
}

.mainWrapLeft {
    width: 17.1875vw;
    margin-left: 2.9vw;
}

.mainWrapLeft .DateBox {
    height: 6vw;
    font-size: 1.4vw;
    padding-top: 1.1vw;
    color: #23cefd;
}

.DateFooter {
    flex: 1;
}

.DateFooter .number {
    font-size: 1.8vw;
    font-family: 'digit';
}

.DateFooter .fengexian {
    width: 1px;
    height: 50%;
    background: #0cbec2;
    margin-left: 4%;
}

.DateFooter .DateUtc {
    font-size: 1.2vw;
    font-weight: 600;
    margin-left: 4%;
}

.title {
    width: 100%;
    text-align: center;
    text-indent: 2vw;
    /*height: 1.9vw;*/
    line-height: 1.7vw;
    font-size: 0.75vw;
    background: url(../../img/index/title1.png) center center/95% 100% no-repeat;
}

.leftBlock{
    background: url(../../img/index/block.png) center center/100% 100% no-repeat;
}

.sailBlock {
    width: 100%;
    height: 8vw;
    margin-top: 0.9vw;
    padding-left: 9%;
    padding-right: 11%;
}

.sailBlock h3 {
    text-align: right;
    font-size: 1.5vw;
}

.sailBlock .process {
    width: 96%;
    height: 1vw;
    border: 2px solid #0cbfc2;
    border-radius: 0.5vw;
    margin-top: 0.3vw;
}

.sailBlock .process span {
    display: block;
    position: relative;
    border-radius: 0.5vw;
    width: 0%;
    transition: all 0.8s;
    height: 100%;
    background-image: linear-gradient(to right, rgba(0, 0, 0, 0.1), #5fa1ff);
}

.sailBlock .process p {
    width: 14.427vw;
    height: 2.6vw;
    background: url(../../img/line.png) no-repeat;
    background-size: 100% 100%;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translate(45%, -40%);
}

.sailBlock .process span img {
    width: 2.5vw;
    position: absolute;
    right: 0%;
    top: 140%;
    transform: translateX(50%);
}

.sailBlock .sailNo {
    color: #0cbfc2;
    font-size: 0.94vw;
    padding-top: 9%;
}

.sailBlock .sailDate {
    font-size: 0.85vw;
    margin-top: 1%;
}

.sailBlock .sailMileage {
    font-size: 0.94vw;
    margin-top: 1.3vw;
    float: right;
}

.sailBlock .sailMileage p {
    color: #0cbfc2;
    margin-top: -0.1vw;
}

.awsData {
    width: 100%;
    height: 8vw;
    margin-top: 0.2vw;
}

.mainWrapCenter {
    width: 56.3vw;
    margin-left: 1.8vw;
    margin-top: 6.1%;;
    position: relative;
}

.mainWrapCenter_zz {
    border: 3px solid #48a2b3;
    box-shadow: inset 0px 0px 70px rgba(0, 156, 255, 0.7);
    -webkitbox-shadow: inset 0px 0px 70px rgba(0, 156, 255, 0.7);
    width: 56.3vw;
    height: 33.5vw;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 2;
}

.mainWrapCenter img {
    width: 100%;
    height: 33.5vw;
}

.mainWrapRight {
    width: 17.1875vw;
    margin-left: 1.8vw;
    position: relative;
    z-index: -1;
}

.mainWrapRight .DateBox {
    width: 100%;
    height: 9.8vw;
    text-align: center;
    position: relative;
}

.mainWrapRight .DateBox .ymd {
    font-size: 1.05vw;
    line-height: 1.8vw;
}

.mainWrapRight .DateBox .currentTime {
    font-size: 2vw;
    line-height: 1.8vw;
}

.mainWrapRight .DateBox .currentTimeFooter {
    font-size: 1.1vw;
    color: #fff;
}

.sailBlock h4 {
    color: #0cbfc2;
    font-size: 1.2vw;
}

.sailBlockItem {
    font-size: 1.2vw;
    padding-top: 0.9vw;
}

.sailBlockItem p {
    padding-left: 10%;
    white-space: nowrap;
}

.directiveFn {
    width: 50%;
    position: relative;
    height: 93%;
    background: url(../../img/directiveFn.png) no-repeat;
    background-size: 100% 100%;
}

.directiveThat {
    width: 10px;
    transition: all 0.4s;
    -webkit-transition: all 0.4s;
    position: absolute;
    left: 50%;
    margin-left: -5px;
    transform-origin: center bottom;
    height: 50%;
    background: url(../../img/directiveThat.png) no-repeat;
    background-size: 100% 100%;
}

.directiveFn1 {
    width: 50%;
    position: relative;
    height: 93%;
    background: url(../../img/directiveFn1.png) no-repeat;
    background-size: 100% 100%;
}

.directiveFn2 {
    width: 50%;
    position: relative;
    height: 93%;
    background: url(../../img/directiveFn2.png) no-repeat;
    background-size: 100% 100%;
}

.directiveThat1 {
    width: 6px;
    transition: all 0.4s;
    -webkit-transition: all 0.4s;
    position: absolute;
    left: 50%;
    bottom: 36%;
    margin-left: -3px;
    transform-origin: center bottom;
    height: 50%;
    background: url(../../img/directiveThat1.png) no-repeat;
    background-size: 100% 100%;
}

.directiveThat2 {
    width: 10px;
    transition: all 0.4s;
    -webkit-transition: all 0.4s;
    position: absolute;
    left: 50%;
    margin-left: -5px;
    transform-origin: center bottom;
    height: 50%;
    background: url(../../img/directiveThat2.png) no-repeat;
    background-size: 100% 100%;
}

.directiveText {
    text-align: center;
    padding-top: 32%;
    font-size: 0.8vw;
}

.footItems {
    width: 19.75vw;
    height: 13vw;
}

#flex1 {
    margin-left: 3.12vw;
}

#flex2 {
    margin-left: 2.28vw;
    width: 19.75vw;
    height: 14vw;
}

.footItems_title {
    text-align: center;
    color: #fff;
    font-size: 0.9vw;
    /*height: 1.4vw;*/
    line-height: 1.8vw;
    margin-top: 1.3vw;
    margin-bottom: 0.45vw;
}

.footItems_cont {
    flex: 1;
    width: 100%;
    /*padding-top: 7px;*/
    /*padding-bottom: 1%;*/
}

.footItems_cont img {
    width: 98%;
    height: 98%;
    border-radius: 0.4vw;
    cursor: pointer;
}

.submenu {
    width: 9.7916vw;
    text-align: center;
    background: rgba(255, 255, 255, 0.79);
    border: 2px solid rgba(12, 191, 195, 0.79);
    position: absolute;
    z-index: 99;
    left: 28vw;
    top: 6vw;
}

.submenu a {
    text-align: center;
    padding-bottom: 2px;
    background: url(../../img/fgx.png) no-repeat center bottom;
    font-weight: 900;
    font-size: 1vw;
    text-shadow: 0px 1.3px 1px rgba(0, 0, 0, 0.4);
    color: #045c5e;
    line-height: 2vw;
    display: block;
    text-stroke: 1px #fff;
    -webkit-text-stroke: 0.5px #fff;
}

.submenu a:last-child {
    background: none;
}

.subtns {
    width: 5.6vw;
    height: 2.1vw;
    cursor: pointer;
    position: absolute;
    left: 28vw;
    top: 3.5vw;
}

.subtns1 {
    width: 5.6vw;
    cursor: pointer;
    position: absolute;
    right: 28vw;
    top: 3.5vw;
}

.backgroundFooter {
    width: 100%;
    height: 2.6vw;
    text-align: center;
    color: #0CBFC2;
    position: absolute;
    left: 0;
    bottom: 0;
}

#mileage {
    margin-left: 9.95vw;
    display: block;
    margin-top: -1.1vw;
}

.pictureList {
    position: absolute;
}

.pictureList li {
    padding-right: 2.45vw;
}
