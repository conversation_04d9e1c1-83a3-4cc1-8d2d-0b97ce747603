/**
 * 坐标转换和计算工具
 * 提供坐标系转换、距离计算、方位角计算等功能
 */

export const coordinateUtils = {
  /**
   * 计算两点间的距离（海里）
   * 使用 Haversine 公式
   * @param {number} lat1 起点纬度
   * @param {number} lng1 起点经度
   * @param {number} lat2 终点纬度
   * @param {number} lng2 终点经度
   * @returns {number} 距离（海里）
   */
  calculateDistance(lat1, lng1, lat2, lng2) {
    const R = 3440.065; // 地球半径（海里）
    const dLat = this.toRadians(lat2 - lat1);
    const dLng = this.toRadians(lng2 - lng1);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  },

  /**
   * 计算两点间的方位角
   * @param {number} lat1 起点纬度
   * @param {number} lng1 起点经度
   * @param {number} lat2 终点纬度
   * @param {number} lng2 终点经度
   * @returns {number} 方位角（度，0-360）
   */
  calculateBearing(lat1, lng1, lat2, lng2) {
    const dLng = this.toRadians(lng2 - lng1);
    const lat1Rad = this.toRadians(lat1);
    const lat2Rad = this.toRadians(lat2);
    
    const y = Math.sin(dLng) * Math.cos(lat2Rad);
    const x = Math.cos(lat1Rad) * Math.sin(lat2Rad) -
              Math.sin(lat1Rad) * Math.cos(lat2Rad) * Math.cos(dLng);
    
    const bearing = this.toDegrees(Math.atan2(y, x));
    return (bearing + 360) % 360;
  },

  /**
   * 根据起点、距离和方位角计算终点坐标
   * @param {number} lat 起点纬度
   * @param {number} lng 起点经度
   * @param {number} distance 距离（海里）
   * @param {number} bearing 方位角（度）
   * @returns {Object} 终点坐标 {lat, lng}
   */
  calculateDestination(lat, lng, distance, bearing) {
    const R = 3440.065; // 地球半径（海里）
    const bearingRad = this.toRadians(bearing);
    const latRad = this.toRadians(lat);
    const lngRad = this.toRadians(lng);
    
    const lat2Rad = Math.asin(
      Math.sin(latRad) * Math.cos(distance / R) +
      Math.cos(latRad) * Math.sin(distance / R) * Math.cos(bearingRad)
    );
    
    const lng2Rad = lngRad + Math.atan2(
      Math.sin(bearingRad) * Math.sin(distance / R) * Math.cos(latRad),
      Math.cos(distance / R) - Math.sin(latRad) * Math.sin(lat2Rad)
    );
    
    return {
      lat: this.toDegrees(lat2Rad),
      lng: this.toDegrees(lng2Rad)
    };
  },

  /**
   * 计算多边形的面积（平方海里）
   * @param {Array} coordinates 坐标数组 [{lat, lng}, ...]
   * @returns {number} 面积（平方海里）
   */
  calculatePolygonArea(coordinates) {
    if (coordinates.length < 3) return 0;
    
    const R = 3440.065; // 地球半径（海里）
    let area = 0;
    
    for (let i = 0; i < coordinates.length; i++) {
      const j = (i + 1) % coordinates.length;
      const lat1 = this.toRadians(coordinates[i].lat);
      const lat2 = this.toRadians(coordinates[j].lat);
      const lng1 = this.toRadians(coordinates[i].lng);
      const lng2 = this.toRadians(coordinates[j].lng);
      
      area += (lng2 - lng1) * (2 + Math.sin(lat1) + Math.sin(lat2));
    }
    
    area = Math.abs(area * R * R / 2);
    return area;
  },

  /**
   * 判断点是否在多边形内
   * @param {Object} point 点坐标 {lat, lng}
   * @param {Array} polygon 多边形坐标数组
   * @returns {boolean} 是否在多边形内
   */
  isPointInPolygon(point, polygon) {
    let inside = false;
    const x = point.lng;
    const y = point.lat;
    
    for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
      const xi = polygon[i].lng;
      const yi = polygon[i].lat;
      const xj = polygon[j].lng;
      const yj = polygon[j].lat;
      
      if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
        inside = !inside;
      }
    }
    
    return inside;
  },

  /**
   * 计算点到线段的最短距离
   * @param {Object} point 点坐标 {lat, lng}
   * @param {Object} lineStart 线段起点 {lat, lng}
   * @param {Object} lineEnd 线段终点 {lat, lng}
   * @returns {number} 距离（海里）
   */
  distanceToLineSegment(point, lineStart, lineEnd) {
    const A = point.lat - lineStart.lat;
    const B = point.lng - lineStart.lng;
    const C = lineEnd.lat - lineStart.lat;
    const D = lineEnd.lng - lineStart.lng;
    
    const dot = A * C + B * D;
    const lenSq = C * C + D * D;
    
    if (lenSq === 0) {
      return this.calculateDistance(point.lat, point.lng, lineStart.lat, lineStart.lng);
    }
    
    let param = dot / lenSq;
    
    let closestPoint;
    if (param < 0) {
      closestPoint = lineStart;
    } else if (param > 1) {
      closestPoint = lineEnd;
    } else {
      closestPoint = {
        lat: lineStart.lat + param * C,
        lng: lineStart.lng + param * D
      };
    }
    
    return this.calculateDistance(point.lat, point.lng, closestPoint.lat, closestPoint.lng);
  },

  /**
   * WGS84 转 GCJ02 (火星坐标系)
   * @param {number} lng 经度
   * @param {number} lat 纬度
   * @returns {Object} 转换后的坐标 {lat, lng}
   */
  wgs84ToGcj02(lng, lat) {
    if (this.isOutOfChina(lng, lat)) {
      return { lng, lat };
    }
    
    let dLat = this.transformLat(lng - 105.0, lat - 35.0);
    let dLng = this.transformLng(lng - 105.0, lat - 35.0);
    
    const radLat = lat / 180.0 * Math.PI;
    let magic = Math.sin(radLat);
    magic = 1 - 0.00669342162296594323 * magic * magic;
    const sqrtMagic = Math.sqrt(magic);
    
    dLat = (dLat * 180.0) / ((6378245.0 * (1 - 0.00669342162296594323)) / (magic * sqrtMagic) * Math.PI);
    dLng = (dLng * 180.0) / (6378245.0 / sqrtMagic * Math.cos(radLat) * Math.PI);
    
    return {
      lat: lat + dLat,
      lng: lng + dLng
    };
  },

  /**
   * GCJ02 转 WGS84
   * @param {number} lng 经度
   * @param {number} lat 纬度
   * @returns {Object} 转换后的坐标 {lat, lng}
   */
  gcj02ToWgs84(lng, lat) {
    if (this.isOutOfChina(lng, lat)) {
      return { lng, lat };
    }
    
    let dLat = this.transformLat(lng - 105.0, lat - 35.0);
    let dLng = this.transformLng(lng - 105.0, lat - 35.0);
    
    const radLat = lat / 180.0 * Math.PI;
    let magic = Math.sin(radLat);
    magic = 1 - 0.00669342162296594323 * magic * magic;
    const sqrtMagic = Math.sqrt(magic);
    
    dLat = (dLat * 180.0) / ((6378245.0 * (1 - 0.00669342162296594323)) / (magic * sqrtMagic) * Math.PI);
    dLng = (dLng * 180.0) / (6378245.0 / sqrtMagic * Math.cos(radLat) * Math.PI);
    
    return {
      lat: lat - dLat,
      lng: lng - dLng
    };
  },

  /**
   * 验证坐标是否有效
   * @param {number} lng 经度
   * @param {number} lat 纬度
   * @returns {boolean} 是否有效
   */
  isValidCoordinate(lng, lat) {
    return typeof lng === 'number' && 
           typeof lat === 'number' && 
           !isNaN(lng) && 
           !isNaN(lat) &&
           lng >= -180 && lng <= 180 &&
           lat >= -90 && lat <= 90;
  },

  /**
   * 格式化坐标显示
   * @param {number} lng 经度
   * @param {number} lat 纬度
   * @param {string} format 格式类型 'decimal' | 'dms'
   * @returns {Object} 格式化结果
   */
  formatCoordinate(lng, lat, format = 'decimal') {
    if (format === 'dms') {
      return {
        lng: this.toDMS(lng, 'lng'),
        lat: this.toDMS(lat, 'lat')
      };
    }
    
    return {
      lng: lng.toFixed(6) + '°',
      lat: lat.toFixed(6) + '°'
    };
  },

  /**
   * 转换为度分秒格式
   * @param {number} decimal 十进制度数
   * @param {string} type 类型 'lng' | 'lat'
   * @returns {string} 度分秒字符串
   */
  toDMS(decimal, type) {
    const absolute = Math.abs(decimal);
    const degrees = Math.floor(absolute);
    const minutesFloat = (absolute - degrees) * 60;
    const minutes = Math.floor(minutesFloat);
    const seconds = ((minutesFloat - minutes) * 60).toFixed(2);
    
    let direction;
    if (type === 'lng') {
      direction = decimal >= 0 ? 'E' : 'W';
    } else {
      direction = decimal >= 0 ? 'N' : 'S';
    }
    
    return `${degrees}°${minutes}'${seconds}"${direction}`;
  },

  /**
   * 角度转弧度
   */
  toRadians(degrees) {
    return degrees * (Math.PI / 180);
  },

  /**
   * 弧度转角度
   */
  toDegrees(radians) {
    return radians * (180 / Math.PI);
  },

  /**
   * 判断是否在中国境外
   */
  isOutOfChina(lng, lat) {
    return lng < 72.004 || lng > 137.8347 || lat < 0.8293 || lat > 55.8271;
  },

  /**
   * 坐标转换辅助函数 - 纬度
   */
  transformLat(lng, lat) {
    let ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng));
    ret += (20.0 * Math.sin(6.0 * lng * Math.PI) + 20.0 * Math.sin(2.0 * lng * Math.PI)) * 2.0 / 3.0;
    ret += (20.0 * Math.sin(lat * Math.PI) + 40.0 * Math.sin(lat / 3.0 * Math.PI)) * 2.0 / 3.0;
    ret += (160.0 * Math.sin(lat / 12.0 * Math.PI) + 320 * Math.sin(lat * Math.PI / 30.0)) * 2.0 / 3.0;
    return ret;
  },

  /**
   * 坐标转换辅助函数 - 经度
   */
  transformLng(lng, lat) {
    let ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));
    ret += (20.0 * Math.sin(6.0 * lng * Math.PI) + 20.0 * Math.sin(2.0 * lng * Math.PI)) * 2.0 / 3.0;
    ret += (20.0 * Math.sin(lng * Math.PI) + 40.0 * Math.sin(lng / 3.0 * Math.PI)) * 2.0 / 3.0;
    ret += (150.0 * Math.sin(lng / 12.0 * Math.PI) + 300.0 * Math.sin(lng / 30.0 * Math.PI)) * 2.0 / 3.0;
    return ret;
  }
}

export default coordinateUtils
