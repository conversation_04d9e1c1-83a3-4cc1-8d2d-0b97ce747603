/**
 * 轨迹处理混入
 * 提供轨迹数据处理、优化和动画功能
 */

import { coordinateUtils } from '@/utils/map/coordinateUtils'
import { mapConfig } from '@/utils/map/mapConfig'

export const trackMixin = {
  data() {
    return {
      trackData: new Map(), // 存储轨迹数据
      trackLines: new Map(), // 存储轨迹线
      trackAnimations: new Map(), // 存储动画状态
      trackColors: [...mapConfig.trackColors], // 轨迹颜色池
      usedColors: new Set() // 已使用的颜色
    }
  },

  methods: {
    /**
     * 添加轨迹数据
     * @param {string} trackId 轨迹ID
     * @param {Array} points 轨迹点数组
     * @param {Object} options 选项
     */
    addTrackData(trackId, points, options = {}) {
      if (!points || points.length === 0) return

      // 处理和优化轨迹点
      const processedPoints = this.processTrackPoints(points)
      
      const trackInfo = {
        id: trackId,
        points: processedPoints,
        originalPoints: points,
        color: options.color || this.getNextTrackColor(),
        visible: options.visible !== false,
        animated: options.animated || false,
        style: options.style || 'default',
        metadata: options.metadata || {},
        createdAt: new Date(),
        updatedAt: new Date()
      }

      this.trackData.set(trackId, trackInfo)
      
      if (trackInfo.visible) {
        this.createTrackLine(trackId)
      }

      this.$emit('track-added', trackInfo)
      return trackInfo
    },

    /**
     * 更新轨迹数据
     * @param {string} trackId 轨迹ID
     * @param {Array} newPoints 新的轨迹点
     * @param {boolean} append 是否追加到现有轨迹
     */
    updateTrackData(trackId, newPoints, append = false) {
      const trackInfo = this.trackData.get(trackId)
      if (!trackInfo) return

      let points
      if (append) {
        points = [...trackInfo.originalPoints, ...newPoints]
      } else {
        points = newPoints
      }

      const processedPoints = this.processTrackPoints(points)
      
      trackInfo.points = processedPoints
      trackInfo.originalPoints = points
      trackInfo.updatedAt = new Date()

      this.trackData.set(trackId, trackInfo)

      if (trackInfo.visible) {
        this.updateTrackLine(trackId)
      }

      this.$emit('track-updated', trackInfo)
      return trackInfo
    },

    /**
     * 移除轨迹
     * @param {string} trackId 轨迹ID
     */
    removeTrack(trackId) {
      const trackInfo = this.trackData.get(trackId)
      if (!trackInfo) return

      // 移除轨迹线
      this.removeTrackLine(trackId)
      
      // 停止动画
      this.stopTrackAnimation(trackId)
      
      // 释放颜色
      this.usedColors.delete(trackInfo.color)
      
      // 删除数据
      this.trackData.delete(trackId)

      this.$emit('track-removed', trackInfo)
    },

    /**
     * 处理轨迹点数据
     * @param {Array} points 原始轨迹点
     * @returns {Array} 处理后的轨迹点
     */
    processTrackPoints(points) {
      if (!points || points.length === 0) return []

      let processedPoints = []

      // 数据清洗和验证
      points.forEach(point => {
        const lng = point.longitude || point.lng
        const lat = point.latitude || point.lat
        
        if (coordinateUtils.isValidCoordinate(lng, lat)) {
          processedPoints.push({
            lng: Number(lng),
            lat: Number(lat),
            timestamp: point.timestamp || point.time || Date.now(),
            speed: point.speed || 0,
            heading: point.heading || point.course || 0,
            altitude: point.altitude || 0
          })
        }
      })

      // 按时间排序
      processedPoints.sort((a, b) => a.timestamp - b.timestamp)

      // 去重（相同位置的点）
      processedPoints = this.removeDuplicatePoints(processedPoints)

      // 平滑处理
      processedPoints = this.smoothTrackPoints(processedPoints)

      // 限制点数量
      if (processedPoints.length > mapConfig.performance.maxTrackPoints) {
        processedPoints = this.simplifyTrackPoints(processedPoints, mapConfig.performance.maxTrackPoints)
      }

      return processedPoints
    },

    /**
     * 去除重复点
     * @param {Array} points 轨迹点数组
     * @returns {Array} 去重后的轨迹点
     */
    removeDuplicatePoints(points) {
      const result = []
      const threshold = 0.00001 // 约1米的精度

      for (let i = 0; i < points.length; i++) {
        const current = points[i]
        const previous = result[result.length - 1]

        if (!previous) {
          result.push(current)
          continue
        }

        const distance = Math.abs(current.lng - previous.lng) + Math.abs(current.lat - previous.lat)
        if (distance > threshold) {
          result.push(current)
        }
      }

      return result
    },

    /**
     * 平滑轨迹点
     * @param {Array} points 轨迹点数组
     * @returns {Array} 平滑后的轨迹点
     */
    smoothTrackPoints(points) {
      if (points.length < 3) return points

      const smoothed = [points[0]] // 保留第一个点

      for (let i = 1; i < points.length - 1; i++) {
        const prev = points[i - 1]
        const current = points[i]
        const next = points[i + 1]

        // 简单的移动平均平滑
        const smoothedPoint = {
          lng: (prev.lng + current.lng + next.lng) / 3,
          lat: (prev.lat + current.lat + next.lat) / 3,
          timestamp: current.timestamp,
          speed: current.speed,
          heading: current.heading,
          altitude: current.altitude
        }

        smoothed.push(smoothedPoint)
      }

      smoothed.push(points[points.length - 1]) // 保留最后一个点
      return smoothed
    },

    /**
     * 简化轨迹点（道格拉斯-普克算法）
     * @param {Array} points 轨迹点数组
     * @param {number} maxPoints 最大点数
     * @returns {Array} 简化后的轨迹点
     */
    simplifyTrackPoints(points, maxPoints) {
      if (points.length <= maxPoints) return points

      // 简单的等间隔采样
      const step = Math.floor(points.length / maxPoints)
      const simplified = []

      for (let i = 0; i < points.length; i += step) {
        simplified.push(points[i])
      }

      // 确保包含最后一个点
      if (simplified[simplified.length - 1] !== points[points.length - 1]) {
        simplified.push(points[points.length - 1])
      }

      return simplified
    },

    /**
     * 创建轨迹线
     * @param {string} trackId 轨迹ID
     */
    createTrackLine(trackId) {
      const trackInfo = this.trackData.get(trackId)
      if (!trackInfo || !this.map) return

      const points = trackInfo.points.map(point => new BM.LngLat(point.lng, point.lat))
      
      const styleConfig = mapConfig.trackStyles[trackInfo.style] || mapConfig.trackStyles.default
      
      const polyline = new BM.Polyline(points, {
        strokeColor: trackInfo.color,
        strokeWeight: styleConfig.strokeWeight,
        strokeOpacity: styleConfig.strokeOpacity,
        strokeStyle: styleConfig.strokeStyle
      })

      this.map.addOverlay(polyline)
      this.trackLines.set(trackId, polyline)

      // 绑定事件
      polyline.on('click', (e) => {
        this.$emit('track-click', { trackId, trackInfo, event: e })
      })

      return polyline
    },

    /**
     * 更新轨迹线
     * @param {string} trackId 轨迹ID
     */
    updateTrackLine(trackId) {
      this.removeTrackLine(trackId)
      this.createTrackLine(trackId)
    },

    /**
     * 移除轨迹线
     * @param {string} trackId 轨迹ID
     */
    removeTrackLine(trackId) {
      const polyline = this.trackLines.get(trackId)
      if (polyline && this.map) {
        this.map.removeOverlay(polyline)
        this.trackLines.delete(trackId)
      }
    },

    /**
     * 显示/隐藏轨迹
     * @param {string} trackId 轨迹ID
     * @param {boolean} visible 是否可见
     */
    setTrackVisible(trackId, visible) {
      const trackInfo = this.trackData.get(trackId)
      if (!trackInfo) return

      trackInfo.visible = visible

      if (visible) {
        this.createTrackLine(trackId)
      } else {
        this.removeTrackLine(trackId)
      }

      this.$emit('track-visibility-changed', { trackId, visible })
    },

    /**
     * 设置轨迹颜色
     * @param {string} trackId 轨迹ID
     * @param {string} color 颜色
     */
    setTrackColor(trackId, color) {
      const trackInfo = this.trackData.get(trackId)
      if (!trackInfo) return

      const oldColor = trackInfo.color
      trackInfo.color = color

      // 更新颜色使用状态
      this.usedColors.delete(oldColor)
      this.usedColors.add(color)

      // 更新轨迹线
      if (trackInfo.visible) {
        this.updateTrackLine(trackId)
      }

      this.$emit('track-color-changed', { trackId, color, oldColor })
    },

    /**
     * 获取下一个可用的轨迹颜色
     * @returns {string} 颜色值
     */
    getNextTrackColor() {
      for (const color of this.trackColors) {
        if (!this.usedColors.has(color)) {
          this.usedColors.add(color)
          return color
        }
      }

      // 如果所有颜色都被使用，随机选择一个
      const randomIndex = Math.floor(Math.random() * this.trackColors.length)
      const color = this.trackColors[randomIndex]
      this.usedColors.add(color)
      return color
    },

    /**
     * 开始轨迹动画
     * @param {string} trackId 轨迹ID
     * @param {Object} options 动画选项
     */
    startTrackAnimation(trackId, options = {}) {
      const trackInfo = this.trackData.get(trackId)
      if (!trackInfo || trackInfo.points.length < 2) return

      this.stopTrackAnimation(trackId)

      const animationState = {
        trackId,
        currentIndex: 0,
        isPlaying: true,
        speed: options.speed || 1,
        loop: options.loop || false,
        onProgress: options.onProgress,
        onComplete: options.onComplete
      }

      this.trackAnimations.set(trackId, animationState)
      this.animateTrack(trackId)

      this.$emit('track-animation-started', { trackId, options })
    },

    /**
     * 停止轨迹动画
     * @param {string} trackId 轨迹ID
     */
    stopTrackAnimation(trackId) {
      const animationState = this.trackAnimations.get(trackId)
      if (animationState) {
        animationState.isPlaying = false
        this.trackAnimations.delete(trackId)
        this.$emit('track-animation-stopped', { trackId })
      }
    },

    /**
     * 执行轨迹动画
     * @param {string} trackId 轨迹ID
     */
    animateTrack(trackId) {
      const animationState = this.trackAnimations.get(trackId)
      const trackInfo = this.trackData.get(trackId)
      
      if (!animationState || !trackInfo || !animationState.isPlaying) return

      const { currentIndex, speed } = animationState
      const points = trackInfo.points

      if (currentIndex >= points.length - 1) {
        if (animationState.loop) {
          animationState.currentIndex = 0
        } else {
          this.stopTrackAnimation(trackId)
          if (animationState.onComplete) {
            animationState.onComplete(trackId)
          }
          return
        }
      }

      // 绘制当前段的轨迹
      const currentPoints = points.slice(0, currentIndex + 2)
      this.drawPartialTrack(trackId, currentPoints)

      // 触发进度回调
      if (animationState.onProgress) {
        const progress = currentIndex / (points.length - 1)
        animationState.onProgress(trackId, progress, points[currentIndex])
      }

      animationState.currentIndex++

      // 继续动画
      setTimeout(() => {
        this.animateTrack(trackId)
      }, 100 / speed)
    },

    /**
     * 绘制部分轨迹
     * @param {string} trackId 轨迹ID
     * @param {Array} points 轨迹点
     */
    drawPartialTrack(trackId, points) {
      const trackInfo = this.trackData.get(trackId)
      if (!trackInfo || !this.map) return

      // 移除现有轨迹线
      this.removeTrackLine(trackId)

      // 创建新的轨迹线
      const bmPoints = points.map(point => new BM.LngLat(point.lng, point.lat))
      const styleConfig = mapConfig.trackStyles[trackInfo.style] || mapConfig.trackStyles.default
      
      const polyline = new BM.Polyline(bmPoints, {
        strokeColor: trackInfo.color,
        strokeWeight: styleConfig.strokeWeight,
        strokeOpacity: styleConfig.strokeOpacity,
        strokeStyle: styleConfig.strokeStyle
      })

      this.map.addOverlay(polyline)
      this.trackLines.set(trackId, polyline)
    },

    /**
     * 获取轨迹信息
     * @param {string} trackId 轨迹ID
     * @returns {Object} 轨迹信息
     */
    getTrackInfo(trackId) {
      return this.trackData.get(trackId)
    },

    /**
     * 获取所有轨迹
     * @returns {Map} 所有轨迹数据
     */
    getAllTracks() {
      return this.trackData
    },

    /**
     * 清除所有轨迹
     */
    clearAllTracks() {
      this.trackData.forEach((trackInfo, trackId) => {
        this.removeTrack(trackId)
      })
      this.trackData.clear()
      this.trackLines.clear()
      this.trackAnimations.clear()
      this.usedColors.clear()

      this.$emit('all-tracks-cleared')
    },

    /**
     * 适应所有轨迹的边界
     */
    fitAllTracks() {
      const allPoints = []
      
      this.trackData.forEach(trackInfo => {
        if (trackInfo.visible) {
          allPoints.push(...trackInfo.points)
        }
      })

      if (allPoints.length > 0 && this.fitBounds) {
        this.fitBounds(allPoints)
      }
    }
  },

  beforeDestroy() {
    this.clearAllTracks()
  }
}

export default trackMixin
