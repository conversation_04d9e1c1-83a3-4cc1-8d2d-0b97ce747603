.scale-wrap {
    color: #d3d6dd;
    width: 1920px;
    height: 1080px;
    overflow: hidden;
    // &.pageisScale {
    //     position: absolute;
    //     top: 50%;
    //     left: 50%;
    //     transform: translate(-50%, -50%);
    //     transform-origin: left top;
    // }
    .bg {
        width: 100%;
        height: 100%;
        padding: 16px 16px 10px 16px;
        box-sizing: border-box;
        background-image: url("../assets/img/pageBg.png");
        background-size: cover;
        background-position: center center;
    }

    .host-body {
        height: 100%;

        .title_wrap {
            height: 60px;
            background-image: url("../assets/img/top.png");
            background-size: cover;
            background-position: center center;
            position: relative;
            margin-bottom: 4px;

            .guang {
                position: absolute;
                bottom: -26px;
                background-image: url("../assets/img/guang.png");
                background-position: 80px center;
                width: 100%;
                height: 56px;
            }

            .zuojuxing,
            .youjuxing {
                position: absolute;
                top: -2px;
                width: 140px;
                height: 6px;
                background-image: url("../assets/img/headers/juxing1.png");
            }

            .zuojuxing {

                left: 11%;
            }

            .youjuxing {
                right: 11%;
                transform: rotate(180deg);
            }

            .timers {
                position: absolute;
                right: 0;
                top: 30px;
                font-size: 18px;
                display: flex;
                align-items: center;

                .blq-icon-shezhi02 {
                    cursor: pointer;
                }
            }
        }

        .title {
            position: relative;
            // width: 500px;
            text-align: center;
            background-size: cover;
            color: transparent;
            height: 40px;
            line-height: 40px;

            .title-text {
                font-size: 38px;
                font-weight: 900;
                letter-spacing: 6px;
                width: 100%;
                background: linear-gradient(92deg, #0072FF 0%, #00EAFF 48.8525390625%, #01AAFF 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }
        }
    }


}

.scale-wrap {
    .pagetab {
        position: absolute;
        top: -35px;
        display: flex;

        .item {
            width: 130px;
            height: 36px;
            border-radius: 18px 0px 0px 18px;
            color: #00FBF8;
            text-indent: 26px;
            line-height: 36px;
            font-size: 16px;
            margin-right: 20px;
            background: linear-gradient(to right, rgba(76, 245, 255, .5), rgba(76, 245, 255, 0));
        }
    }
}

.setting {
    position: fixed;
    width: 100%;
    height: 100%;
    z-index: 999;
    top: 0;
    left: 0;

    .left_shu {
        color: #000;
        font-weight: 900;
        position: relative;
        text-indent: 10px;
        padding:16px 0 10px 0 ;
        &::before {
            display: block;
            content: " ";
            height: 16px;
            width: 4px;
            border-radius: 2px;
            background: #0072FF;
            position: absolute;
            left: 0px;
        }
    }

    .setting_dislog {
        background-color: rgba($color: #000000, $alpha: .5);
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 0;
        right: 0;
        top: 0;
    }

    .setting_inner {
        box-sizing: border-box;
        background: #FFF;
        width: 268px;
        height: 247px;
        position: absolute;
        right: 0px;
        top: 0;
        z-index: 1;
        color: #000000;
        box-shadow: 0 8px 10px -5px rgba(0, 0, 0, .2), 0 16px 24px 2px rgba(0, 0, 0, .14), 0 6px 30px 5px rgba(0, 0, 0, .12);

        .setting_header {
            font-size: 20px;
            color: rgb(0, 0, 0);
            font-weight: 900;
            text-align: center;
            line-height: 40px;
        }

        .setting_body {
            padding: 0px 16px;
            box-sizing: border-box;
            position: relative;
        }

        .setting_item {
            font-size: 14px;
            line-height: 1.5;

            // display: flex;
            .setting_label {
                color: #555454;
            }
            .setting_label_tip{
                font-size: 12px;
                color: #838282;
            }
        }
    }

    .setting_inner {
        animation: rtl-drawer-out .3s;
    }
}


.settingShow {
    .setting_inner {
        animation: rtl-drawer-in .3s 1ms;
    }

}

.yh-setting-fade-enter-active {
    animation: yh-setting-fade-in .3s;
}

.yh-setting-fade-leave-active {

    animation: yh-setting-fade-out .3s;

}

@keyframes yh-setting-fade-in {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

@keyframes yh-setting-fade-out {
    0% {
        opacity: 1;

    }

    100% {
        opacity: 0;
    }
}


@keyframes rtl-drawer-in {
    0% {
        transform: translate(100%, 0)
    }

    100% {
        -webkit-transform: translate(0, 0);
        transform: translate(0, 0)
    }
}

@keyframes rtl-drawer-out {
    0% {
        transform: translate(0, 0)
    }

    100% {
        transform: translate(100%, 0)
    }
}