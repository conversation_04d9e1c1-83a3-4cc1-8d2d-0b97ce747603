{"remainingRequest": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\indexs\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\indexs\\index.vue", "mtime": 1754276652960}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753075297856}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgTGVmdENvbXBhbnlMaXN0IGZyb20gJy4vbGVmdC1jb21wYW55LWxpc3QudnVlJw0KaW1wb3J0IExlZnRCb3R0b20gZnJvbSAiLi9sZWZ0LWJvdHRvbS52dWUiOw0KaW1wb3J0IENlbnRlck1hcCBmcm9tICIuL2NlbnRlci1tYXAudnVlIjsNCmltcG9ydCBDZW50ZXJCb3R0b20gZnJvbSAiLi9jZW50ZXItYm90dG9tLnZ1ZSI7DQppbXBvcnQgUmlnaHRUb3AgZnJvbSAiLi9yaWdodC10b3AudnVlIjsNCmltcG9ydCBSaWdodENlbnRlciBmcm9tICIuL3JpZ2h0LWNlbnRlci52dWUiOw0KaW1wb3J0IFJpZ2h0Qm90dG9tIGZyb20gIi4vcmlnaHQtYm90dG9tLnZ1ZSI7DQppbXBvcnQgeyBpbml0V2ViU29ja2V0LCBkYXRhTW9kdWxlLCBtYW51YWxDbG9zZSwgY2xlYXJEYXRhIH0gZnJvbSAnQC91dGlscy93ZWJTb2NrZXQnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgY29tcG9uZW50czogew0KICAgIExlZnRDb21wYW55TGlzdCwNCiAgICBMZWZ0Qm90dG9tLA0KICAgIENlbnRlck1hcCwNCiAgICBSaWdodFRvcCwNCiAgICBSaWdodENlbnRlciwNCiAgICBSaWdodEJvdHRvbSwNCiAgICBDZW50ZXJCb3R0b20sDQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICANCiAgICB9Ow0KICB9LA0KICBmaWx0ZXJzOiB7DQogICAgbnVtc0ZpbHRlcihtc2cpIHsNCiAgICAgIHJldHVybiBtc2cgfHwgMDsNCiAgICB9LA0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIA0KICB9LA0KDQogIG1vdW50ZWQoKSB7DQogICAgLy8g57uf5LiA5Yid5aeL5YyWV2ViU29ja2V06L+e5o6l77yM566h55CG5pW05Liq6aG16Z2i55qE5pWw5o2u6I635Y+WDQogICAgdGhpcy5pbml0V2ViU29ja2V0Q29ubmVjdGlvbigpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDnu5/kuIDliJ3lp4vljJZXZWJTb2NrZXTov57mjqUNCiAgICBpbml0V2ViU29ja2V0Q29ubmVjdGlvbigpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOWIneWni+WMlldlYlNvY2tldOi/nuaOpQ0KICAgICAgICBtYW51YWxDbG9zZSgpDQogICAgICAgIGNsZWFyRGF0YSgpDQoNCiAgICAgICAgLy8g6K6+572u6K+***************************************+377yM6YCa6L+H6YCX5Y+35YiG6ZqUDQogICAgICAgIGRhdGFNb2R1bGUuc2VuZHRleHQgPSAidHlwZTY2IzEwMCMwIzBBMDEsMEEwMiwwQTAzLDBBMDQiDQoNCiAgICAgICAgaW5pdFdlYlNvY2tldCgpDQogICAgICAgIGNvbnNvbGUubG9nKCfkuLvpobXpnaJXZWJTb2NrZXTov57mjqXlt7LliJ3lp4vljJbvvIzor7fmsYLmlbDmja7nvJblj7c6JywgZGF0YU1vZHVsZS5zZW5kdGV4dCkNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+S4u+mhtemdoldlYlNvY2tldOi/nuaOpeWksei0pTonLCBlcnJvcikNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8gLy8g5aSE55CG5a2Q57uE5Lu255qE6YeN6L+e6K+35rGCDQogICAgLy8gaGFuZGxlV2ViU29ja2V0UmVjb25uZWN0KCkgew0KICAgIC8vICAgY29uc29sZS5sb2coJ+aUtuWIsOWtkOe7hOS7tumHjei/nuivt+axgu+8jOmHjeaWsOWIneWni+WMlldlYlNvY2tldOi/nuaOpScpDQogICAgLy8gICB0aGlzLmluaXRXZWJTb2NrZXRDb25uZWN0aW9uKCkNCiAgICAvLyB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA6DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/indexs", "sourcesContent": ["<!--\r\n * @Author: daidai\r\n * @Date: 2022-03-04 09:23:59\r\n * @LastEditors: Please set LastEditors\r\n * @LastEditTime: 2022-05-07 11:05:02\r\n * @FilePath: \\web-pc\\src\\pages\\big-screen\\view\\indexs\\index.vue\r\n-->\r\n<template>\r\n  <div class=\"contents\">\r\n    <div class=\"contetn_left\">\r\n      <div class=\"pagetab\">\r\n        <!-- <div class=\"item\">实时监测</div> -->\r\n        \r\n      </div>\r\n      <ItemWrap class=\"contetn_left-company contetn_lr-item-large\" title=\"企业列表\">\r\n        <LeftCompanyList />\r\n      </ItemWrap>\r\n      <ItemWrap\r\n        class=\"contetn_left-bottom contetn_lr-item\"\r\n        title=\"设备提醒\"\r\n        style=\"padding: 0 10px 16px 10px\"\r\n      >\r\n        <LeftBottom />\r\n      </ItemWrap>\r\n    </div>\r\n    <div class=\"contetn_center\">\r\n      <CenterMap class=\"contetn_center_top\" />\r\n      <!--\r\n      <ItemWrap class=\"contetn_center-bottom\" title=\"安装计划\">\r\n        <CenterBottom />\r\n      </ItemWrap>\r\n      -->\r\n    </div>\r\n    <!--\r\n    <div class=\"contetn_right\">\r\n      <ItemWrap\r\n        class=\"contetn_left-bottom contetn_lr-item\"\r\n        title=\"报警次数\"\r\n      >\r\n        <RightTop />\r\n      </ItemWrap>\r\n      <ItemWrap\r\n        class=\"contetn_left-bottom contetn_lr-item\"\r\n        title=\"报警排名(TOP8)\"\r\n        style=\"padding: 0 10px 16px 10px\"\r\n      >\r\n        <RightCenter />\r\n      </ItemWrap>\r\n      \r\n      <ItemWrap\r\n        class=\"contetn_left-bottom contetn_lr-item\"\r\n        title=\"数据统计图 \"\r\n      >\r\n        <RightBottom />\r\n      </ItemWrap>\r\n    </div>\r\n    -->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport LeftCompanyList from './left-company-list.vue'\r\nimport LeftBottom from \"./left-bottom.vue\";\r\nimport CenterMap from \"./center-map.vue\";\r\nimport CenterBottom from \"./center-bottom.vue\";\r\nimport RightTop from \"./right-top.vue\";\r\nimport RightCenter from \"./right-center.vue\";\r\nimport RightBottom from \"./right-bottom.vue\";\r\nimport { initWebSocket, dataModule, manualClose, clearData } from '@/utils/webSocket'\r\n\r\nexport default {\r\n  components: {\r\n    LeftCompanyList,\r\n    LeftBottom,\r\n    CenterMap,\r\n    RightTop,\r\n    RightCenter,\r\n    RightBottom,\r\n    CenterBottom,\r\n  },\r\n  data() {\r\n    return {\r\n    \r\n    };\r\n  },\r\n  filters: {\r\n    numsFilter(msg) {\r\n      return msg || 0;\r\n    },\r\n  },\r\n  created() {\r\n    \r\n  },\r\n\r\n  mounted() {\r\n    // 统一初始化WebSocket连接，管理整个页面的数据获取\r\n    this.initWebSocketConnection()\r\n  },\r\n  methods: {\r\n    // 统一初始化WebSocket连接\r\n    initWebSocketConnection() {\r\n      try {\r\n        // 初始化WebSocket连接\r\n        manualClose()\r\n        clearData()\r\n\r\n        // 设置请求所有需要的数据编号，通过逗号分隔\r\n        dataModule.sendtext = \"type66#100#0#0A01,0A02,0A03,0A04\"\r\n\r\n        initWebSocket()\r\n        console.log('主页面WebSocket连接已初始化，请求数据编号:', dataModule.sendtext)\r\n      } catch (error) {\r\n        console.error('主页面WebSocket连接失败:', error)\r\n      }\r\n    },\r\n\r\n    // // 处理子组件的重连请求\r\n    // handleWebSocketReconnect() {\r\n    //   console.log('收到子组件重连请求，重新初始化WebSocket连接')\r\n    //   this.initWebSocketConnection()\r\n    // },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n// 内容\r\n.contents {\r\n  .contetn_left,\r\n  .contetn_right {\r\n    width: 430px;\r\n    box-sizing: border-box;\r\n  }\r\n\r\n  .contetn_left {\r\n    height: 960px;\r\n    gap: 10px;\r\n  }\r\n\r\n  .contetn_center {\r\n    height: 960px;\r\n    width: 1439px;\r\n  }\r\n\r\n  //左右两侧 三个块\r\n  .contetn_lr-item {\r\n    height: 310px;\r\n  }\r\n\r\n  // 企业列表区域\r\n  .contetn_lr-item-large {\r\n    height: 635px;\r\n  }\r\n\r\n  .contetn_center_top {\r\n    width: 100%;\r\n  }\r\n\r\n  // 中间\r\n  .contetn_center {\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-around;\r\n  }\r\n\r\n  .contetn_center-bottom {\r\n    height: 315px;\r\n  }\r\n\r\n  //左边 右边 结构一样\r\n  .contetn_left,\r\n  .contetn_right {\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-around;\r\n    position: relative;\r\n  }\r\n}\r\n\r\n\r\n@keyframes rotating {\r\n    0% {\r\n        -webkit-transform: rotate(0) scale(1);\r\n        transform: rotate(0) scale(1);\r\n    }\r\n    50% {\r\n        -webkit-transform: rotate(180deg) scale(1.1);\r\n        transform: rotate(180deg) scale(1.1);\r\n    }\r\n    100% {\r\n        -webkit-transform: rotate(360deg) scale(1);\r\n        transform: rotate(360deg) scale(1);\r\n    }\r\n}\r\n</style>\r\n"]}]}