/**
 * 九段线数据
 * 从 boatApp 迁移的九段线地理数据
 */

export const nineLineData = {
  "type": "FeatureCollection",
  "features": [
    {
      "type": "Feature",
      "geometry": {
        "type": "LineString",
        "coordinates": [[122.50662231445312, 23.467723846435547], [122.78874206542969, 24.572216033935547]]
      },
      "properties": {
        "name": "0",
        "stroke": "#ffffff",
        "stroke-opacity": 1,
        "fill-opacity": 0,
        "Style": "PEN(c:#ffffff)"
      }
    }, 
    {
      "type": "Feature",
      "geometry": {
        "type": "LineString",
        "coordinates": [[121.17402648925781, 20.826547622680664], [121.91168975830078, 21.6975154876709]]
      },
      "properties": {
        "name": "1",
        "stroke": "#ffffff",
        "stroke-opacity": 1,
        "fill-opacity": 0,
        "Style": "PEN(c:#ffffff)"
      }
    }, 
    {
      "type": "Feature",
      "geometry": {
        "type": "LineString",
        "coordinates": [[119.47308349609375, 18.01557731628418], [119.48887634277344, 18.049428939819336], [119.50243377685547, 18.078767776489258], [119.51821899414062, 18.10810661315918], [119.53401947021484, 18.141958236694336], [119.55207061767578, 18.17806625366211], [119.57463836669922, 18.2254581451416], [119.5949478149414, 18.261566162109375], [119.61752319335938, 18.302188873291016], [119.63556671142578, 18.340553283691406], [119.65363311767578, 18.369892120361328], [119.67167663574219, 18.403743743896484], [119.69831848144531, 18.452810287475586], [119.72453308105469, 18.492509841918945], [119.74395751953125, 18.53055191040039], [119.75969696044922, 18.557205200195312], [119.77464294433594, 18.590312957763672], [119.78978729248047, 18.613773345947266], [119.82063293457031, 18.6700439453125], [119.84996795654297, 18.724205017089844], [119.87478637695312, 18.764827728271484], [119.89510345458984, 18.805450439453125], [119.91992950439453, 18.846071243286133], [119.94925689697266, 18.893463134765625], [119.97183227539062, 18.943113327026367], [119.99891662597656, 18.985992431640625], [120.01923370361328, 19.024356842041016]]
      },
      "properties": {
        "name": "2",
        "stroke": "#ffffff",
        "stroke-opacity": 1,
        "fill-opacity": 0,
        "Style": "PEN(c:#ffffff)"
      }
    }, 
    {
      "type": "Feature",
      "geometry": {
        "type": "LineString",
        "coordinates": [[109.301513671875, 16.199544906616186], [109.3283462524414, 16.157794952392557], [109.3638916015625, 16.113210678100636], [109.39987182617188, 16.066280364990185], [109.42801666259766, 16.02873611450196], [109.44673919677734, 16.005300521850604], [109.48052215576172, 15.965022087097168], [109.52198791503906, 15.91834259033204], [109.54692077636719, 15.880122184753406], [109.58133697509766, 15.836319923400893], [109.60166931152344, 15.800339698791515], [109.62670135498047, 15.762795448303219], [109.64703369140625, 15.728379249572782], [109.66893768310547, 15.68927097320555], [109.68771362304688, 15.651725769042967], [109.70648193359375, 15.614182472229004], [109.72625732421875, 15.572297096252445], [109.75341033935547, 15.509369850158627], [109.7768783569336, 15.446795463562074], [109.79094696044922, 15.402993202209485], [109.80034637451172, 15.363883972167937], [109.81598663330078, 15.32164669036861], [109.83162689208984, 15.265330314636234], [109.84101867675781, 15.22309207916265], [109.8472671508789, 15.185547828674398], [109.85196685791016, 15.166775703430156], [109.85508728027344, 15.158953666687026]]
      },
      "properties": {
        "name": "3",
        "stroke": "#ffffff",
        "stroke-opacity": 1,
        "fill-opacity": 0,
        "Style": "PEN(c:#ffffff)"
      }
    }
  ]
}

/**
 * 获取九段线数据
 * @returns {Object} GeoJSON格式的九段线数据
 */
export function getNineLineJson() {
  return nineLineData
}

/**
 * 创建九段线图层
 * @param {Object} map 地图实例
 * @param {Object} options 样式选项
 * @returns {Array} 九段线图层数组
 */
export function createNineLineLayer(map, options = {}) {
  if (!map || !window.BM) return []

  const defaultOptions = {
    strokeColor: '#ffffff',
    strokeWeight: 2,
    strokeOpacity: 1,
    fillOpacity: 0,
    ...options
  }

  const layers = []

  nineLineData.features.forEach(feature => {
    if (feature.geometry.type === 'LineString') {
      const coordinates = feature.geometry.coordinates
      const points = coordinates.map(coord => new BM.LngLat(coord[0], coord[1]))
      
      const polyline = new BM.Polyline(points, {
        strokeColor: defaultOptions.strokeColor,
        strokeWeight: defaultOptions.strokeWeight,
        strokeOpacity: defaultOptions.strokeOpacity
      })

      map.addOverlay(polyline)
      layers.push(polyline)
    }
  })

  return layers
}

/**
 * 移除九段线图层
 * @param {Object} map 地图实例
 * @param {Array} layers 九段线图层数组
 */
export function removeNineLineLayer(map, layers) {
  if (!map || !layers) return

  layers.forEach(layer => {
    if (layer) {
      map.removeOverlay(layer)
    }
  })
}

export default {
  nineLineData,
  getNineLineJson,
  createNineLineLayer,
  removeNineLineLayer
}
