/**
 * 地图工具函数
 * 提供地图操作的通用工具方法
 */

import { mapConfig } from './mapConfig'
import { coordinateUtils } from './coordinateUtils'

export const mapUtils = {
  /**
   * 创建地图实例
   * @param {string} containerId 地图容器ID
   * @param {Object} options 地图选项
   * @returns {Object} 地图实例
   */
  createMap(containerId, options = {}) {
    if (!window.BM) {
      throw new Error('Bigemap library is not loaded')
    }

    // 设置全局配置
    BM.Config.HTTP_URL = mapConfig.mapHost
    BM.accessToken = mapConfig.accessToken || 'pk.eyJ1IjoiY3VzXzg3a3J5a3Y4IiwiYSI6IjdmZzdsdWI0eDVtYmF6ZWRoOWxudmt2ZDEifQ.hbJM3hwBW1MVSGg3eiWdNQ'

    const defaultOptions = {
      center: [mapConfig.defaultCenter[0], mapConfig.defaultCenter[1]],
      zoom: mapConfig.defaultZoom,
      zoomControl: true,
      minZoom: 3,
      maxZoom: 18,
      attributionControl: false,
      ...options
    }

    // 使用正确的API调用方式
    const map = BM.map(containerId, 'bigemap.bvgwuwcf', defaultOptions)

    // 添加默认图层
    const satelliteLayer = BM.tileLayer('bigemap.satellite')
    satelliteLayer.addTo(map)

    return map
  },

  /**
   * 创建图标
   * @param {Object} iconConfig 图标配置
   * @returns {Object} 图标实例
   */
  createIcon(iconConfig) {
    if (!window.BM) return null

    return new BM.Icon({
      url: iconConfig.url,
      size: new BM.Size(iconConfig.size[0], iconConfig.size[1]),
      anchor: new BM.Size(iconConfig.anchor[0], iconConfig.anchor[1])
    })
  },

  /**
   * 创建标记
   * @param {number} lng 经度
   * @param {number} lat 纬度
   * @param {Object} options 标记选项
   * @returns {Object} 标记实例
   */
  createMarker(lng, lat, options = {}) {
    if (!window.BM) return null

    const position = new BM.LngLat(lng, lat)
    return new BM.Marker(position, options)
  },

  /**
   * 创建折线
   * @param {Array} points 点数组
   * @param {Object} options 折线选项
   * @returns {Object} 折线实例
   */
  createPolyline(points, options = {}) {
    if (!window.BM || !points || points.length < 2) return null

    const bmPoints = points.map(point => {
      if (point instanceof BM.LngLat) {
        return point
      }
      return new BM.LngLat(point.lng || point.longitude, point.lat || point.latitude)
    })

    const defaultOptions = {
      strokeColor: '#ff0000',
      strokeWeight: 3,
      strokeOpacity: 0.8,
      ...options
    }

    return new BM.Polyline(bmPoints, defaultOptions)
  },

  /**
   * 创建多边形
   * @param {Array} points 点数组
   * @param {Object} options 多边形选项
   * @returns {Object} 多边形实例
   */
  createPolygon(points, options = {}) {
    if (!window.BM || !points || points.length < 3) return null

    const bmPoints = points.map(point => {
      if (point instanceof BM.LngLat) {
        return point
      }
      return new BM.LngLat(point.lng || point.longitude, point.lat || point.latitude)
    })

    const defaultOptions = {
      strokeColor: '#ff0000',
      strokeWeight: 2,
      strokeOpacity: 0.8,
      fillColor: '#ff0000',
      fillOpacity: 0.2,
      ...options
    }

    return new BM.Polygon(bmPoints, defaultOptions)
  },

  /**
   * 创建圆形
   * @param {number} lng 中心经度
   * @param {number} lat 中心纬度
   * @param {number} radius 半径（米）
   * @param {Object} options 圆形选项
   * @returns {Object} 圆形实例
   */
  createCircle(lng, lat, radius, options = {}) {
    if (!window.BM) return null

    const center = new BM.LngLat(lng, lat)
    const defaultOptions = {
      strokeColor: '#00ff00',
      strokeWeight: 2,
      strokeOpacity: 0.8,
      fillColor: '#00ff00',
      fillOpacity: 0.2,
      ...options
    }

    return new BM.Circle(center, radius, defaultOptions)
  },

  /**
   * 创建信息窗口
   * @param {string} content 窗口内容
   * @param {number} lng 经度
   * @param {number} lat 纬度
   * @param {Object} options 窗口选项
   * @returns {Object} 信息窗口实例
   */
  createInfoWindow(content, lng, lat, options = {}) {
    if (!window.BM) return null

    const position = new BM.LngLat(lng, lat)
    const defaultOptions = {
      maxWidth: mapConfig.popup.maxWidth,
      minWidth: mapConfig.popup.minWidth,
      ...options
    }

    const infoWindow = new BM.InfoWindow(content, defaultOptions)
    return { infoWindow, position }
  },

  /**
   * 添加图层到地图
   * @param {Object} map 地图实例
   * @param {string} layerType 图层类型
   * @returns {Object} 图层实例
   */
  addLayer(map, layerType) {
    if (!map || !window.BM) return null

    const layerConfig = mapConfig.layers[layerType]
    if (!layerConfig) {
      console.warn(`Unknown layer type: ${layerType}`)
      return null
    }

    const layer = new BM.TileLayer({
      url: layerConfig.url,
      attribution: layerConfig.attribution
    })

    map.addLayer(layer)
    return layer
  },

  /**
   * 移除图层
   * @param {Object} map 地图实例
   * @param {Object} layer 图层实例
   */
  removeLayer(map, layer) {
    if (map && layer) {
      map.removeLayer(layer)
    }
  },

  /**
   * 适应边界
   * @param {Object} map 地图实例
   * @param {Array} points 点数组
   * @param {Object} options 选项
   */
  fitBounds(map, points, options = {}) {
    if (!map || !points || points.length === 0) return

    const bounds = new BM.LatLngBounds()
    points.forEach(point => {
      const lng = point.lng || point.longitude
      const lat = point.lat || point.latitude
      if (lng !== undefined && lat !== undefined) {
        bounds.extend(new BM.LngLat(lng, lat))
      }
    })

    const defaultOptions = {
      padding: 50,
      ...options
    }

    map.fitBounds(bounds, defaultOptions)
  },

  /**
   * 获取地图边界
   * @param {Object} map 地图实例
   * @returns {Object} 边界对象
   */
  getBounds(map) {
    if (!map) return null

    const bounds = map.getBounds()
    return {
      northeast: {
        lng: bounds.getNorthEast().lng,
        lat: bounds.getNorthEast().lat
      },
      southwest: {
        lng: bounds.getSouthWest().lng,
        lat: bounds.getSouthWest().lat
      }
    }
  },

  /**
   * 屏幕坐标转地理坐标
   * @param {Object} map 地图实例
   * @param {number} x 屏幕X坐标
   * @param {number} y 屏幕Y坐标
   * @returns {Object} 地理坐标
   */
  pixelToLngLat(map, x, y) {
    if (!map) return null

    const lngLat = map.pixelToPoint(new BM.Pixel(x, y))
    return {
      lng: lngLat.lng,
      lat: lngLat.lat
    }
  },

  /**
   * 地理坐标转屏幕坐标
   * @param {Object} map 地图实例
   * @param {number} lng 经度
   * @param {number} lat 纬度
   * @returns {Object} 屏幕坐标
   */
  lngLatToPixel(map, lng, lat) {
    if (!map) return null

    const pixel = map.pointToPixel(new BM.LngLat(lng, lat))
    return {
      x: pixel.x,
      y: pixel.y
    }
  },

  /**
   * 计算两点间距离
   * @param {number} lng1 起点经度
   * @param {number} lat1 起点纬度
   * @param {number} lng2 终点经度
   * @param {number} lat2 终点纬度
   * @returns {number} 距离（海里）
   */
  calculateDistance(lng1, lat1, lng2, lat2) {
    return coordinateUtils.calculateDistance(lat1, lng1, lat2, lng2)
  },

  /**
   * 计算方位角
   * @param {number} lng1 起点经度
   * @param {number} lat1 起点纬度
   * @param {number} lng2 终点经度
   * @param {number} lat2 终点纬度
   * @returns {number} 方位角（度）
   */
  calculateBearing(lng1, lat1, lng2, lat2) {
    return coordinateUtils.calculateBearing(lat1, lng1, lat2, lng2)
  },

  /**
   * 格式化坐标
   * @param {number} lng 经度
   * @param {number} lat 纬度
   * @param {number} precision 精度
   * @returns {Object} 格式化后的坐标
   */
  formatCoordinate(lng, lat, precision = 6) {
    return {
      lng: Number(lng.toFixed(precision)),
      lat: Number(lat.toFixed(precision)),
      lngStr: lng.toFixed(precision) + '°E',
      latStr: lat.toFixed(precision) + '°N'
    }
  },

  /**
   * 验证坐标有效性
   * @param {number} lng 经度
   * @param {number} lat 纬度
   * @returns {boolean} 是否有效
   */
  isValidCoordinate(lng, lat) {
    return coordinateUtils.isValidCoordinate(lng, lat)
  },

  /**
   * 创建聚合器
   * @param {Object} map 地图实例
   * @param {Array} markers 标记数组
   * @param {Object} options 聚合选项
   * @returns {Object} 聚合器实例
   */
  createMarkerCluster(map, markers = [], options = {}) {
    if (!window.BM || !window.BmapLib) return null

    const defaultOptions = {
      ...mapConfig.cluster,
      ...options
    }

    const markerCluster = new BmapLib.MarkerClusterer(map, {
      markers: markers,
      ...defaultOptions
    })

    return markerCluster
  },

  /**
   * 获取最佳缩放级别
   * @param {Array} points 点数组
   * @returns {number} 缩放级别
   */
  getBestZoomLevel(points) {
    if (!points || points.length === 0) return mapConfig.defaultZoom

    if (points.length === 1) return 15

    // 计算点的分布范围
    let minLng = points[0].lng || points[0].longitude
    let maxLng = minLng
    let minLat = points[0].lat || points[0].latitude
    let maxLat = minLat

    points.forEach(point => {
      const lng = point.lng || point.longitude
      const lat = point.lat || point.latitude
      
      minLng = Math.min(minLng, lng)
      maxLng = Math.max(maxLng, lng)
      minLat = Math.min(minLat, lat)
      maxLat = Math.max(maxLat, lat)
    })

    const lngDiff = maxLng - minLng
    const latDiff = maxLat - minLat
    const maxDiff = Math.max(lngDiff, latDiff)

    // 根据范围确定缩放级别
    if (maxDiff > 10) return 5
    if (maxDiff > 5) return 6
    if (maxDiff > 2) return 7
    if (maxDiff > 1) return 8
    if (maxDiff > 0.5) return 9
    if (maxDiff > 0.2) return 10
    if (maxDiff > 0.1) return 11
    if (maxDiff > 0.05) return 12
    if (maxDiff > 0.02) return 13
    if (maxDiff > 0.01) return 14
    return 15
  },

  /**
   * 销毁地图实例
   * @param {Object} map 地图实例
   */
  destroyMap(map) {
    if (map && typeof map.destroy === 'function') {
      map.destroy()
    }
  }
}

export default mapUtils
