@font-face {
    font-family: "enBlob";
    src: url('/fonts/AlibabaSans-Bold.otf') format('truetype');
}

@font-face {
    font-family: "enRegular";
    /*src: url('/fonts/AlibabaSans-Regular.otf') format('truetype');*/
}

* {
    margin: 0;
    padding: 0px;
    box-sizing: border-box;
}

::-webkit-scrollbar {
    width: 0px;
}

ul li {
    list-style: none;
}

a {
    text-decoration: none;
}

img {
    max-width: 100%;
    display: block;
}

html,
body {
    width: 100%;
    height: 100%;
    background: #020412;
    font-family: "enRegular";
    color: #fff;
}

.flex {
    display: flex;
    display: -webkit-flex;
}

.flex.jlr {
    justify-content: space-between;
}

.flex.j-center {
    justify-content: center;
}

.flex.a-center {
    align-items: center;
}

.flex.col {
    flex-direction: column;
}

.flex.wrap {
    flex-wrap: wrap;
}

.flex.j-end {
    justify-content: flex-end;
}

.flex.a-end {
    align-items: flex-end;
}


.background, .backgroundImg {
    width: 100%;
    position: relative;
}

.head {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 2;
    width: 100%;
    height: 10%;
}

.main {
    position: absolute;
    left: 0;
    top: 15%;
    z-index: 2;
    width: 100%;
    height: 85%;
}

.leftWrap {
    width: 30.1vw;
    padding-left: 3.5%;
}

.timer {
    font-size: 1.4vw;
    color: #23cefd;
    margin-left: -5%;
    font-weight: 500;
    margin-top: 3%;
    margin-bottom: 10.3%;
}

.timer h4 {
    font-weight: 400;
}

.rightWrap {
    width: 30.1vw;
    padding-right: 3.5%;
}

.subtns {
    width: 5.6vw;
    height: 2vw;
    cursor: pointer;
    position: absolute;
    z-index: 99;
    left: 28vw;
    top: 3.5vw;
    text-align: center;
}

.subtns .menuFont {
    position: absolute;
    font-size: 1.2em;
    font-weight: bold;
    letter-spacing: 0.2em;
    margin: 0 0 0 1.4em;
    color: #33f0fe;
    text-shadow: 3px 3px 2px rgba(0, 0, 0, 0.4);
}

.subtns1 {
    width: 5.6vw;
    height: 2.1vw;
    cursor: pointer;
    position: absolute;
    z-index: 99;
    right: 28vw;
    top: 3.5vw;
    text-align: center;
}

.subtns1 p {
    font-size: 1.2em;
    font-weight: bold;
    letter-spacing: 0.2em;
    margin: 0.1em 0;
    color: #33f0fe;
    text-shadow: 3px 3px 2px rgba(0, 0, 0, 0.4);
}

.box {
    width: 0px;
    height: 0px;
    position: absolute;
    margin: 0.6em 0 0 0.8em;
    border-top: 0.9em solid white;
    border-left: 0.5em solid transparent;
    border-right: 0.5em solid transparent;
    border-bottom: 0.9em solid transparent;
}


.backgroundFooter {
    width: 100%;
    height: 3.2vw;
    text-align: center;
    color: #0CBFC2;
    position: absolute;
    left: 0;
    bottom: -0.3vw;
    font-size: 1vw
}

.submenu {
    width: 9.7916vw;
    text-align: center;
    background: rgba(255, 255, 255, 0.79);
    border: 2px solid rgba(12, 191, 195, 0.79);
    position: absolute;
    z-index: 99;
    left: 28vw;
    top: 6vw;
}

.submenu a {
    text-align: center;
    padding-bottom: 2px;
    background: url(/img/fgx.png) no-repeat center bottom;
    font-weight: 900;
    font-size: 1vw;
    text-shadow: 0 1.3px 1px rgba(0, 0, 0, 0.4);
    color: #045c5e;
    line-height: 2vw;
    display: block;
    text-stroke: 1px #fff;
    -webkit-text-stroke: 0.5px #fff;
}

.submenu a:last-child {
    background: none;
}

.container {
    width: 100%;
    height: 100%
}

/*.row {*/
/*width: 100%;*/
/*height: 50%;*/
/*}*/

.col-xs-4 {
    width: 35%;
    height: 100%;
    margin-left: 11vw;
    margin-top: 1vw;
}

.title_img {
    width: 100%;
    text-align: center;
    font-size: 1.2vw;

}

.title_img p {
    padding-top: 0.3vw;
}

.picture {
    width: 100%;
    height: 80%;

}

.picture img {
    width: 93.5%;
    height: 90.5%;
    margin-top: 1.02vw;
    border-radius: 0.6vw;
    margin-left: 0.45vw;
}


.submenu a {
    text-decoration: none;
}

.fade {
    opacity: 0;
    -webkit-transition: opacity .15s linear;
    -o-transition: opacity .15s linear;
    transition: opacity .15s linear;
}

.fade.in {
    opacity: 1;
}

button.close {
    -webkit-appearance: none;
    padding: 0;
    cursor: pointer;
    background: transparent;
    border: 0;
}

.modal-open {
    overflow: hidden;
}

.modal {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1050;
    display: none;
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
    outline: 0;
}

.modal.fade .modal-dialog {
    -webkit-transition: -webkit-transform .3s ease-out;
    -o-transition: -o-transform .3s ease-out;
    transition: transform .3s ease-out;
    -webkit-transform: translate(0, -25%);
    -ms-transform: translate(0, -25%);
    -o-transform: translate(0, -25%);
    transform: translate(0, -25%);
}

.modal.in .modal-dialog {
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
}

.modal-open .modal {
    overflow-x: hidden;
    overflow-y: auto;
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: 10px;
}

.modal-content {
    position: relative;
    background-color: #fff;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    border: 1px solid #999;
    border: 1px solid rgba(0, 0, 0, .2);
    border-radius: 6px;
    outline: 0;
    -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, .5);
    box-shadow: 0 3px 9px rgba(0, 0, 0, .5);
}

.modal-backdrop {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1040;
    background-color: #000;
}

.modal-backdrop.fade {
    filter: alpha(opacity=0);
    opacity: 0;
}

.modal-backdrop.in {
    filter: alpha(opacity=50);
    opacity: .5;
}

.modal-header {
    min-height: 16.42857143px;
    padding: 15px;
    border-bottom: 1px solid #e5e5e5;
}

.modal-header .close {
    margin-top: -2px;
}

.modal-title {
    margin: 0;
    line-height: 1.42857143;
}

.modal-body {
    position: relative;
    padding: 15px;
}

.modal-footer {
    padding: 15px;
    text-align: right;
    border-top: 1px solid #e5e5e5;
}

.modal-footer .btn + .btn {
    margin-bottom: 0;
    margin-left: 5px;
}

.modal-footer .btn-group .btn + .btn {
    margin-left: -1px;
}

.modal-footer .btn-block + .btn-block {
    margin-left: 0;
}

.modal-scrollbar-measure {
    position: absolute;
    top: -9999px;
    width: 50px;
    height: 50px;
    overflow: scroll;
}

@media (min-width: 768px) {
    .modal-dialog {
        width: 600px;
        margin: 30px auto;
    }

    .modal-content {
        -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, .5);
        box-shadow: 0 5px 15px rgba(0, 0, 0, .5);
    }

    .modal-sm {
        width: 300px;
    }
}

@media (min-width: 992px) {
    .modal-lg {
        width: 900px;
    }
}

.modal-footer:before,
.modal-footer:after {
    display: table;
    content: " ";
}

#myModal {
    width: 100%;
    height: 100%;
}

.modal-dialog {
    width: 66%;
    height: 100%;
    margin-top: 6%;
}

.modal-body {
    padding: 0;
}

.camera {
    width: 100%;
}

.close {
    width: 1.8vw;
    position: absolute;
    right: 0.5vw;
    top: 0.2vw;
}

.play {
    width: 1.7vw;
    position: absolute;
    right: 2.5vw;
    top: 0.25vw;
}

.close:hover, .play:hover {
    cursor: pointer;
}

/*#modalHeard{ width:100%;height: 2.5vw;background: rgba(0,16,27,0.7);display: none;position: absolute;}*/


.domainName {
    position: relative;
    width: 100%;
    text-align: center;
    top: -2%;
}

.titleLabel {
    width: 32.08vw;
    height: 5.6875vw;
    position: absolute;
    left: 50%;
    top: 1.4%;
    transform: translateX(-50%);
    font-size: 4vw;
    font-weight: 700;
    text-align: center;
    letter-spacing: 10px;
    background-image: -webkit-linear-gradient(bottom, #5298ff, #7fefff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.back {
    position: absolute;
    top: 1vw;
    left: 18%;
}

.slider-container {
    width: 64vw;
    /*height: 36vw;*/
    margin: 8vw auto;
}

#fullscreen, #exitFullscreen {
    width: 1.8vw;
    position: absolute;
    right: 0;
    z-index: 999;
    margin-top: 0.4vw;
}

#forward, #back {
    width: 1.8vw;
    margin-top: -1.4vw;
    z-index: 999;
    position: absolute;
    color: #ffffff;
    font-size: 20px;
    cursor: pointer;
}

#speed {
    width: 1.8vw;
    height: 1vw;
    margin-top: -1.2vw;
    z-index: 999;
    position: absolute;
    color: #ffffff;
    cursor: pointer;
    border: 1px solid #ffffff;
    right: 5.1vw;
    text-align: center;
}

#fullscreen:hover {
    cursor: pointer;
}

#exitFullscreen:hover {
    cursor: pointer;
}

#imgSlider {
    width: 100%;
    height: 100%;
    /* overflow控制溢出时的样式，隐藏/滚动 */
    overflow: hidden;
}

#imgSlider .imgSlides {
    display: block;
    width: 3000px;
}

#imgSlider .imgSlide {
    float: left;
    /* list-style-type 属性设置列表项标记的类型。 */
    list-style-type: none;
}

#imgSlider img {
    width: 100%;
}

.btnClass {
    background-color: #C9C5C5;
}

.showBgFont {
    margin-top: 0.2em;
    font-size: 4.5em;
    letter-spacing: 0.2em;
    text-shadow: 0 0 2px #fff,
    0 0 3px #fff,
    0 0 10px #2f6994,
    0 0 15px #265e94,
    0 0 20px #153e94;
}

.showTitle {
    background: url(../../img/index/title2.png) center center/100% 100% no-repeat;
    line-height: 3em;
    margin-bottom: 0.4em;
    text-align: center;
    font-size: 1.2em;
}

.showImg {
    width:100%;
    height:100%;
    border: 2px solid #1cb2ff;
    border-radius: 0.6vw;
}


