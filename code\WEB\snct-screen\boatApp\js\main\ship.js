//地图
let map;
let zoomLevel = 5;
let moveTime = 0;
// 船只列表
let shipList;
let shipMap;
// 航次列表
let cruiseMap;
// 艏向列表
let hehdtMap;
// GPS列表
let gpsListMap;
let coordinateMap;
let shipImg=new Map();
let shipObj=new Map();
let shipMarkerMap = new Map();
let pathPatternMap = new Map();
let firstOrNot = true
let toggleLeft = false
let showLineFlag = false
let imgList=[];
let circleNum=0
// 备航中
let inPreparation = false;

//建立的连接
let ws = null;
//是否真正建立连接
let lockReconnect = false;
let flagBoll = true;
//断开 重连倒计时
let timeoutNum = null;
//30秒一次心跳
let timeout = 15 * 60 * 1000;
//心跳心跳倒计时
let timeoutObj = null;
//心跳倒计时
let serverTimeoutObj = null;
let snapTimeoutObj = null;
let colorArr = ['#FFDB6F', '#23FDF0', '#8AAFEC','#FF8955', '#21a40a'];

$(function () {
    for(let i in colorArr){
        shipImg.set(colorArr[i],'../img/ship/shipList/position'+i+'.png')
    }
    $.cookie('CurrentColor',colorArr[0])
    //初始化地图
    // initMap();
    getInfo();
    initDataNew();
    initDate();
    createWebSocket();
    // start();
    // reset();
    // reconnect();
});
function refInitData(){
    setTimeout(initDate, 300*1001);
    if (!flagBoll){
        refreshSnapshot();
    }else {
        clearTimeout(snapTimeoutObj);
    }
}

function getSnapshot() {
    if(imgList.length===0){
        $('#img0').css('display','none')
        $('#imgText0').css('display','none')
        $('#img1').css('display','none')
        $('#imgText1').css('display','none')
    }
    if(imgList.length === 1){
        $('#img0').attr('src',imgList[0].directory+'/'+imgList[0].fileName)
        $('#imgText0').text(imgList[0].channelName)
        $('#img0').css('display','none')
        $('#imgText0').css('display','none')
        $('#img1').css('display','none')
        $('#imgText1').css('display','none')
    }
    if(imgList.length === 2){
        $('#img0').attr('src',imgList[0].directory+'/'+imgList[0].fileName)
        $('#imgText0').text(imgList[0].channelName)
        $('#img1').attr('src',imgList[1].directory+'/'+imgList[1].fileName)
        $('#imgText1').text(imgList[1].channelName)
        $('#img0').css('display','block')
        $('#imgText0').css('display','block')
        $('#img1').css('display','block')
        $('#imgText1').css('display','block')
    }
    if(imgList.length >2 ){
        // 返回图片总数为偶数
        if(imgList.length%2===0){
            if(circleNum === imgList.length-2){
                $('#img0').attr('src',imgList[circleNum].directory+'/'+imgList[circleNum].fileName)
                $('#imgText0').text(imgList[circleNum].channelName)
                $('#img1').attr('src',imgList[circleNum+1].directory+'/'+imgList[circleNum+1].fileName)
                $('#imgText1').text(imgList[circleNum+1].channelName)

                circleNum=0
            }
            else{
                $('#img0').attr('src',imgList[circleNum].directory+'/'+imgList[circleNum].fileName)
                $('#imgText0').text(imgList[circleNum].channelName)
                $('#img1').attr('src',imgList[circleNum+1].directory+'/'+imgList[circleNum+1].fileName)
                $('#imgText1').text(imgList[circleNum+1].channelName)

                circleNum+=2;
            }
        }
        // 返回图片总数为奇数
        else{

            if((circleNum+1) == imgList.length){
                $('#img0').attr('src',imgList[circleNum].directory+'/'+imgList[circleNum].fileName)
                $('#imgText0').text(imgList[circleNum].channelName)
                $('#img1').attr('src',imgList[0].directory+'/'+imgList[0].fileName)
                $('#imgText1').text(imgList[0].channelName)
                $('#img0').css('display','block')
                $('#img1').css('display','block')
                $('#imgText0').css('display','block')
                $('#imgText1').css('display','block')

                circleNum=0
            }
            else{
                $('#img0').attr('src',imgList[circleNum].directory+'/'+imgList[circleNum].fileName)
                $('#imgText0').text(imgList[circleNum].channelName)
                $('#img1').attr('src',imgList[circleNum+1].directory+'/'+imgList[circleNum+1].fileName)
                $('#imgText1').text(imgList[circleNum+1].channelName)
                $('#img0').css('display','block')
                $('#img1').css('display','block')
                $('#imgText0').css('display','block')
                $('#imgText1').css('display','block')


                circleNum+=2;

            }
        }
        refreshSnapshot();
    }


}
function refreshSnapshot() {
    clearTimeout(snapTimeoutObj);
    snapTimeoutObj  = setTimeout(getSnapshot, 5000);
}
//开启心跳
function start() {
    timeoutObj && clearTimeout(timeoutObj);
    serverTimeoutObj && clearTimeout(serverTimeoutObj);
    timeoutObj = setTimeout(function () {
        //这里发送一个心跳，后端收到后，返回一个心跳消息，
        ws.send("heartCheck");
        serverTimeoutObj = setTimeout(function () {
            //超时关闭
            ws.close();
        }, timeout);
    }, timeout);
}

//重置心跳
function reset() {
    //清除时间
    clearTimeout(timeoutObj);
    clearTimeout(serverTimeoutObj);
    //重启心跳
    start();
}

//重新连接
function reconnect() {
    if (lockReconnect) {
        return;
    }
    lockReconnect = true;
    timeoutNum && clearTimeout(timeoutNum);
    timeoutNum = setTimeout(function () {
        //新连接
        createWebSocket();
        lockReconnect = false;
    }, 5000);
}

//连接websocket
function createWebSocket() {
    let codes = '032A,040A,042A,046A';
    ws = new WebSocket("ws://" + window.location.host + laputaWsHost + "/websocket/" + codes + "/" + getCurrentSn());
    ws.onopen = function () {
        console.log('WebSocket已打开: ');
        //开启心跳
        start();
    };

    ws.onmessage = function (e) {
        let dataMap = JSON.parse(e.data);
        handleMessage(dataMap);
        reset();

        // let currentTime = Math.round(new Date());
        // console.log(document.getElementById('restore'),'')
        // //判断当前时间与操作时间的间隔，大于30秒则调取轨迹四周边界
        // if (document.getElementById("restore").title === "暂停自动缩放") {
        //     if (currentTime > (moveTime + 1000 * 40)) {
        //         map.fitBounds(pathPattern.getBounds());
        //     }
        // }
    };

    ws.onclose = function (e) {
        console.log('WebSocket关闭: ');
        // console.log(e);
        //重连
        reconnect();
    };

    ws.onerror = function (e) {
        console.log('WebSocket发生错误: ');
        // console.log(e);
        //重连
        reconnect();
    };

    // 监听窗口关闭事件，当窗口关闭时，主动去关闭websocket连接，防止连接还没断开就关闭窗口，server端会抛异常。
    window.onbeforeunload = function () {
        ws.close();
    };

}

function handleMessage(dataMap) {

    if (dataMap === undefined ) {
        return;
    }
    //船向
    if (dataMap[getCurrentSn() + '_042A'] !== undefined && dataMap[getCurrentSn() + '_042A'] !== null) {
        let magData = dataMap[getCurrentSn() + '_042A'];
        hehdt = magData.hehdt;
        // syncHehdt(parseInt(magData.hehdt));
    }

    // 船gps 经纬度 时间 船速
    if (dataMap[getCurrentSn() + '_032A'] !== undefined && dataMap[getCurrentSn() + '_032A'] !== null) {
        let gpsData = dataMap[getCurrentSn() + '_032A'];
        let latitude = formatLat(gpsData.latitude);
        let longitude = formatLong(gpsData.longitude);
        let bjTime = gpsData.initialBjTime;
        let groundRateJ = changeTwoDecimal_f(gpsData.groundRateJ, 2);
        let utcTime = formatDate(new Date(bjTime) - 8 * 3600 * 1000);

        //给元素传值
        syncGps(latitude, longitude, groundRateJ, utcTime);
    }

    //风向、风速
    // if (dataMap[getCurrentSn() + '_046A'] !== undefined && dataMap[getCurrentSn() + '_046A'] !== null) {
    //     let windData = dataMap[getCurrentSn() + '_046A'];
    //     let trueWind = windData.relativeWind;
    //     let trueWindSpeed = changeTwoDecimal_f(windData.relativeWindSpeed, 1);
    //     // syncWind(trueWind, trueWindSpeed);
    // }

    // if (dataMap[getCurrentSn() + '_040A'] !== undefined && dataMap[getCurrentSn() + '_040A'] !== null) {
    //     let logData = dataMap[getCurrentSn() + '_040A'];
    //     count++;
    //     if (count === 60 * 5) {
    //         getCruise();
    //         count = 1;
    //     }
    //     let mileage = changeTwoDecimal_f(logData.totalShipMileage - initLog, 2);
    //     syncLog(mileage);
    // }
}

// function syncCruise() {
//     let processLine;
//     if (new Date().getTime() - startTime < 0) {
//         processLine = 0;
//     } else {
//         let rate = Math.floor((new Date().getTime() - startTime) / (endTime - startTime) * 10000) / 100;
//         processLine = rate > 100 ? 100 : rate;
//     }
//     document.getElementById("processLine").innerText = processLine + '%';
//     document.getElementById("cruiseCode").innerText = cruiseCode + '航次';
//     document.getElementById("date").innerText = dataTimesFilter(startTime) + '-' + dataTimesFilter(endTime);
//     document.getElementById("shipImg").style.width = (processLine) + '%';
//     document.getElementsByClassName("title")[0].innerText = shipName + '执行情况';
// }

//将websocket传过来的gps信息放进元素中
function syncGps(latitude, longitude, groundRateJ, utcTime) {
    document.getElementById("latitude").innerText = latitude;
    document.getElementById("longtitude").innerText = longitude;
    document.getElementById("shipTime").innerText = utcTime;
    document.getElementById("shipSpeed").innerText = groundRateJ + '节';
    // document.getElementById("shipS").style.transform = "rotate(" + (groundRateJ * 130 / 27 * 3 - 130) + "deg)";
}

//将websocket传过来的aws信息放进元素中
// function syncWind(trueWind, trueWindSpeed) {
//     document.getElementById("trueW").innerText = trueWind + '°';
//     document.getElementById("trueR").style.transform = "rotate(" + trueWind + "deg)";
//     document.getElementById("trueWindS").innerText = trueWindSpeed + '节';
//     document.getElementById("wSpeed").style.transform = "rotate(" + (trueWindSpeed * 130 / 27 - 130) + "deg)";
// }
//
// //将websocket传过来的compass信息放进元素中
// function syncHehdt(hehdt) {
//     document.getElementById("hehdt").innerText = hehdt + '°';
//     document.getElementsByClassName("directiveThat")[0].style.transform = "rotate(" + hehdt + "deg)";
// }
//
// function syncLog(mileage) {
//     document.getElementById("mileage").innerText = mileage + 'nm';
// }











function initDataNew() {
    let initSn;
    // 获取船只的船名，状态
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/ship/list",
        dataType: "json",
        async: false,
        success: function (result) {
            if (result.data === undefined) {
                return;
            }
            shipList = result.data;

            initSn = shipList[0].sn
            setCurrentSn(initSn)
            $('#baseInfo').text(shipList[0].name)
            $('#shipName').text(shipList[0].name)
            if (shipList[0].status === 1) {
                $('#shipStatus').text('在航')
            } else {
                $('#shipStatus').text('停泊')
            }

        }
    });
// 获取图片
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/snapshot/log/newest/" + getCurrentSn(),
        dataType: "json",
        success: function (result) {

            imgList = result.data
            if (imgList.length>2){
                flagBoll = false;
            }else{
                flagBoll = true;
            }
            if(imgList.length === 0){
                $('#img0').css('display','none')
                $('#imgText0').css('display','none')
                $('#img1').css('display','none')
                $('#imgText1').css('display','none')
            }
            if(imgList.length === 1) {
                $('#img0').attr('src',imgList[0].directory+'/'+imgList[0].fileName)
                $('#imgText0').text(imgList[0].channelName)
                $('#img1').css('display','none')
                $('#imgText1').css('display','none')
                $('#img0').css('display','block')
                $('#imgText0').css('display','block')
            }
            if(imgList.length >= 2 ){
                $('#img0').css('display','block')
                $('#imgText0').css('display','block')
                $('#img1').css('display','block')
                $('#imgText1').css('display','block')
                $('#img0').attr('src',imgList[0].directory+'/'+imgList[0].fileName)
                $('#imgText0').text(imgList[0].channelName)
                $('#img1').attr('src',imgList[1].directory+'/'+imgList[1].fileName)
                $('#imgText1').text(imgList[1].channelName)
            }
            refInitData();
        },
    });


}

// 初始化显示信息
function initDate() {

    // 航速，经纬度，UTC时间
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/device/data/getLatestData",
        data: {
            sn: getCurrentSn(),
            deviceCode: '032A'
        },
        async: false,
        dataType: "json",
        success: function (result) {
            // console.log(result.data)
            if (result.data === undefined ) {
                return;
            }
            if (Number(result.data.groundRateJ) >= 0) {
                let shipSpeed = Number(result.data.groundRateJ) + '节'
                $('#shipSpeed').text(shipSpeed)
            } else {
                $('#shipSpeed').text('暂无数据')
            }

            if (parseInt(result.data.latitude) >= 0) {
                let latStr = ''
                let latTotal = result.data.latitude
                let latOne = parseInt(latTotal)
                latStr += latOne + '°'
                let latTwo = (latTotal - latOne) * 60
                latStr += Math.trunc(latTwo) + '′'
                let latThree = ((latTwo - parseInt(latTwo)) * 60).toFixed(3)
                latStr += latThree + '″'
                latStr += result.data.latitudeHemisphere
                $('#latitude').text(latStr)
            } else {
                $('#latitude').text('暂无数据')
            }


            if (parseInt(result.data.longitude) >= 0) {
                let longStr = ''
                let longTotal = result.data.longitude
                let longOne = parseInt(longTotal)
                longStr += longOne + '°'
                let longTwo = (longTotal - longOne) * 60
                longStr += Math.trunc(longTwo) + '′'
                let longThree = ((longTwo - parseInt(longTwo)) * 60).toFixed(3)
                longStr += longThree + '″'
                longStr += result.data.longitudeHemisphere
                $('#longtitude').text(longStr)
            } else {
                $('#longtitude').text('暂无数据')
            }


            let UTCTime = new Date(result.data.initialBjTime).toISOString()

            let time1 = UTCTime.substring(0, 10)
            let time2 = UTCTime.substring(11, 19)

            let FinalTime = time1 + ' ' + time2
            $('#shipTime').text(FinalTime)


        }
    });

}

// 获取信息
function getInfo() {
    cleanDataMap();
    initDataMap();
    getShip();
    for (let i = 0; i < shipList.length; i++) {
        // 获取船只的航次列表
        getCruise(shipList[i].sn);
        // if (cruiseMap.get(shipList[i].sn) === undefined) {
        //     continue;
        // }
        // 获取艏向值
        getHehdtList(shipList[i].sn);
        // 获取GPS并画线
        getGpsList(shipList[i].sn, colorArr[i]);
    }
    refresh();
}

// 5分钟刷新一次地图
function refresh() {
    // setTimeout(cleanDataMap, 1000 * 60 * 5);
    setTimeout(getInfo, 1000 * 60 * 5);
}

// 初始化地图
function initDataMap() {
    // 船只信息
    shipList = [];
    // shipMap对象将sn作为键，sn对应信息作为值
    shipMap = new Map();
    // cruiseMap对象将sn作为键，sn对应信息作为值
    cruiseMap = new Map();
    // 暂时未知为何种信息
    hehdtMap = new Map();
    // 船只sn对应航线经纬度点
    gpsListMap = new Map();
    coordinateMap = new Map();
}

// 清空地图数据
function cleanDataMap() {
    shipList = undefined;
    shipMap = undefined;
    cruiseMap = undefined;
    hehdtMap = undefined;
    gpsListMap = undefined;
    coordinateMap = undefined;
}

// 船只列表数据获取
function getShip() {
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/ship/list",
        dataType: "json",
        async: false,
        success: function (result) {

            if (result.data === undefined) {
                return;
            }
            shipList = result.data;
            // 只有一艘船的情况
            if (result.data.length <= 1) {
                appointShip(result.data[0].sn);
                window.location.href = 'index.html';
            }
            if (result.data.length > 1) {

                let htmlStr = '';
                for (let i = 0; i < result.data.length; i++) {
                    // console.log(result.data[i].sn, '船只数据')
                    shipObj.set(result.data[i].sn,true)
                    shipMap.set(result.data[i].sn, result.data[i]);
                    htmlStr += '<p style="font-size: 16px;color: #fff" onclick="appointShip(\'' + result.data[i].sn + '\',\''+colorArr[i]+'\')">' + '<img src="../../img/ship/' + (i + 1) + '.png" style="float: left;vertical-align: bottom;display: inline-block">' + '<input  type="checkbox"   onclick="showShip(\'' + result.data[i].sn + '\',\'' + colorArr[i] + '\')"  checked style="display: inline-block;margin-left: 5%;margin-right: 5%"/>' + result.data[i].name + '</p>';
                }
                $('#shipDiv').html(htmlStr);
            }
        }
    });
}

// 给基本信息的船名和在航状态赋值
function appointShip(sn,color) {
    // console.log(color)
    $.cookie('CurrentColor',color)
    $('.info').css('display', 'block')
    $('.hideText').css('display', 'block')
    for (let i in shipList) {
        if (shipList[i].sn == sn) {
            $('#baseInfo').text(shipList[i].name)
            // 船名
            $('#shipName').text(shipList[i].name)
            // 状态
            if (shipList[i].status === 1) {
                $('#shipStatus').text('在航')
            } else {
                $('#shipStatus').text('离航')
            }
        }
    }
    // 获取图片
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/snapshot/log/newest/" + sn,
        dataType: "json",
        success: function (result) {
            imgList = result.data
            if (imgList.length>2){
                flagBoll = false;
            }else{
                flagBoll = true;
            }
            if(imgList.length === 0){
                $('#img0').css('display','none')
                $('#imgText0').css('display','none')
                $('#img1').css('display','none')
                $('#imgText1').css('display','none')
            }
            if(imgList.length === 1) {
                $('#img0').attr('src',imgList[0].directory+'/'+imgList[0].fileName)
                $('#imgText0').text(imgList[0].channelName)
                $('#img0').css('display','block')
                $('#imgText0').css('display','block')
                $('#img1').css('display','none')
                $('#imgText1').css('display','none')
            }
            if(imgList.length >= 2 ){
                $('#img0').css('display','block')
                $('#imgText0').css('display','block')
                $('#img1').css('display','block')
                $('#imgText1').css('display','block')
                $('#img0').attr('src',imgList[0].directory+'/'+imgList[0].fileName)
                $('#imgText0').text(imgList[0].channelName)
                $('#img1').attr('src',imgList[1].directory+'/'+imgList[1].fileName)
                $('#imgText1').text(imgList[1].channelName)
            }
            //
            // let imgSrc0 =  result.data[0].directory + '/' + result.data[0].fileName
            // let imgSrc1 =  result.data[1].directory + '/' + result.data[1].fileName
            // $('#img0').attr('src', imgSrc0)
            // $('#img1').attr('src', imgSrc1)
            // $('#imgText0').text(result.data[0].channelName)
            // $('#imgText1').text(result.data[1].channelName)
            refInitData();
        },
    });
    // 获取经纬度，船速，UTC时间
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/device/data/getLatestData",
        data: {
            sn: sn,
            deviceCode: '032A'
        },
        async: false,
        dataType: "json",
        success: function (result) {

            if( typeof result.data == "object"){
                if (Number(result.data.groundRateJ) >= 0) {
                    let shipSpeed = Number(result.data.groundRateJ) + '节'
                    $('#shipSpeed').text(shipSpeed)
                } else {
                    $('#shipSpeed').text('暂无数据')
                }

                if (parseInt(result.data.latitude) >= 0) {
                    let latStr = ''
                    let latTotal = result.data.latitude
                    let latOne = parseInt(latTotal)
                    latStr += latOne + '°'
                    let latTwo = (latTotal - latOne) * 60
                    latStr += Math.trunc(latTwo) + '′'
                    let latThree = ((latTwo - parseInt(latTwo)) * 60).toFixed(3)
                    latStr += latThree + '″'
                    latStr += result.data.latitudeHemisphere
                    $('#latitude').text(latStr)
                } else {
                    $('#latitude').text('暂无数据')
                }


                if (parseInt(result.data.longitude) >= 0) {
                    let longStr = ''
                    let longTotal = result.data.longitude
                    let longOne = parseInt(longTotal)
                    longStr += longOne + '°'
                    let longTwo = (longTotal - longOne) * 60
                    longStr += Math.trunc(longTwo) + '′'
                    let longThree = ((longTwo - parseInt(longTwo)) * 60).toFixed(3)
                    longStr += longThree + '″'
                    longStr += result.data.longitudeHemisphere
                    $('#longtitude').text(longStr)
                } else {
                    $('#longtitude').text('暂无数据')
                }

                if (result.data.initialBjTime == undefined) {
                    $('#shipTime').text('暂无数据')
                }
                let UTCTime = new Date(result.data.initialBjTime).toISOString()

                let time1 = UTCTime.substring(0, 10)
                let time2 = UTCTime.substring(11, 19)

                let FinalTime = time1 + ' ' + time2
                $('#shipTime').text(FinalTime)
            }
            else{
                $('#shipSpeed').text('暂无数据')
                $('#longtitude').text('暂无数据')
                $('#latitude').text('暂无数据')
                $('#shipTime').text('暂无数据')
            }
        }

    });

    setCurrentSn(sn)

    reconnect();
}

// 给船只信息的经纬度，UTC时间赋值
function showInfo() {
    $('.info').css('display', 'none')
    $('.hideText').css('display', 'none')
}

//获取所有航次中的最新航次和历史数据起始时间
function getCruise(sn) {
    let timestamp = Date.parse(new Date());
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/shipCruise/queryVoyageInfo/" + sn + '/' + timestamp,
        dataType: "json",
        async: false,
        success: function (result) {

            if (result === undefined || result === '' || result === null) {
                return;
            }
            startTime = result.startTime;
            endTime = result.finishTime;
            if ((startTime === undefined || startTime == null)&&(endTime === undefined || endTime == null)){

            }else{
                cruiseMap.set(sn, result);
            }
            if (timestamp > endTime) {
                inPreparation = true;
            } else {
                inPreparation = false;
            }
        }
    });
}

//获取含有艏向值的数组
function getHehdtList(sn) {
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/device/data/getLatestData",
        data: {
            sn: sn,
            deviceCode: '042A'
        },
        dataType: "json",
        async: false,
        success: function (result) {

            if (result.data === undefined || result.data.hehdt === undefined || result.data.hehdt === null || result.data.hehdt === '') {
                return;
            }
            hehdtMap.set(sn, parseInt(result.data.hehdt));

        }
    });
}

// 回调函数画小船和航线轨迹
function getGpsList(sn, color) {
    let startTime;
    if (cruiseMap.get(sn) === undefined || cruiseMap.get(sn) == null){
        var dateTime=new Date();
         dateTime=dateTime.setDate(dateTime.getDate()-1);
        dateTime=new Date(dateTime);
        startTime = Math.round(dateTime / 1000 * 1000);
    } else{
        startTime = cruiseMap.get(sn).startTime;
        if (inPreparation) {
            startTime = cruiseMap.get(sn).finishTime;
        }
    }

    $.ajax({
        type: "GET",
        url: laputaHost + "/api/device/data/list",
        data: {
            sn: sn,
            deviceCode: '032A',
            startTime: startTime,
            endTime: Math.round(new Date() / 1000 * 1000),
            interval: 15
        },
        dataType: "json",
        async: false,
        success: function (result) {
            if (result.data === undefined || result.data === null || result.data === '') {
                return;
            }
            let gpsList = eval(result.data);
            if (gpsList === undefined || gpsList === null || gpsList === '') {
                return;
            }
            if (map === undefined) {
                initMap(gpsList[gpsList.length - 1]);
            }
            // 获取gps数据
            gpsListMap.set(sn, gpsList);
            // 画小船
            drawShipMarker(sn, gpsList[gpsList.length - 1],color);

            getCoordinate(sn, gpsList);
            if (cruiseMap.get(sn) === undefined || cruiseMap.get(sn) == null){

            }else {
                //画轨迹
                drawPathLine(sn, color);
            }

        }
    });
}

//抽取gps中的经纬度
function getCoordinate(sn, gpsList) {
    let allCoordinate = [];
    for (let i = 0; i < gpsList.length; i++) {
        let attr = [];
        attr.push(parseFloat(gpsList[i][1]));
        attr.push(parseFloat(gpsList[i][0]));
        allCoordinate.push(attr);
    }
    coordinateMap.set(sn, allCoordinate);
}

// 画船标记
function drawShipMarker(sn, gpsArr,color) {
    // console.log(sn,gpsArr,'船只最新位置数据')
    if (shipMarkerMap.get(sn) !== undefined) {
        shipMarkerMap.get(sn).remove();
        shipMarkerMap.delete(sn);
    }
    if (gpsArr === undefined || gpsArr === null || gpsArr === '') {
        return;
    }

    let shipMarker = BM.marker([gpsArr[1], gpsArr[0]],
        {
            rotationAngle: hehdtMap.get(sn),
            icon: BM.icon({
                // 会导致回放船向不再变动
                iconUrl: shipImg.get(color),
                // 大小 x，y
                iconSize: [13, 30],
                // 偏移x,y
                iconAnchor: [0, 0],
                popupAnchor: [-3, -76],
            }),

        },
    ).addTo(map);
    shipMarkerMap.set(sn, shipMarker);

    // shipMarker.on('mouseover', function (e) {
    //     html = getShipMarkerHtml(shipMap.get(sn).name, cruiseMap.get(sn).code, hehdtMap.get(sn), gpsArr);
    //     shipMarker.bindTooltip(html).openTooltip();
    // });
}

//船标记html
function getShipMarkerHtml(shipName, cruiseCode, hehdt, gpsArr) {
    let speed = gpsArr[3];
    let distance = 0;

    if (gpsArr.length >= 5) {
        distance = gpsArr[4].toFixed(2);
    }

    return "<div class='popuoCss'>" +
        "<B>船名：</B>" + shipName + "\<br\>" +
        "<B>航次：</B>" + cruiseCode + "\<br\>" +
        "<B>航速：</B>" + speed + "节" + "\<br\>" +
        "<B>艏向：</B>" + hehdt + "°" + "\<br\>" +
        '<B>距初始位置：</B>' + distance + '海里\<br\>' +
        "<B>经纬度：</B>" + formatLat(gpsArr[1]) + "," + formatLong(gpsArr[0]) + "\<br\>" +
        "<B>数据时间：</B>" + transformTime(gpsArr[2]) + "\<br\></div>";
}

//画轨迹
function drawPathLine(sn, color) {
    // console.log(sn,color)
    if (gpsListMap.get(sn) === undefined || gpsListMap.get(sn).length === 0 || coordinateMap.get(sn).length === 0) {
        return;
    }
    if (pathPatternMap.get(sn) !== undefined) {
        pathPatternMap.get(sn).remove();
        pathPatternMap.delete(sn);
    }
    //绘制航线
    let pathPattern = BM.polylineDecorator(
        coordinateMap.get(sn),
        {
            patterns: [
                // 轨迹
                {
                    offset: 5,
                    repeat: 15,
                    symbol: BM.Symbol.dash({
                        pixelSize: 9,
                        pathOptions: {
                            // color: '#ff6347',
                            color: color,
                            weight: 1.5
                        }
                    })
                },
                // 航线箭头
                {
                    offset: 43,
                    repeat: 70,
                    symbol: BM.Symbol.arrowHead({
                        headAngle: 40,
                        pixelSize: 15,
                        // color: '#ff6347',
                        color: color,
                        fillOpacity: 1,
                        weight: 1
                    })
                },
                // 航线经过的点
                {
                    offset: 0,
                    repeat: 70,
                    symbol: BM.Symbol.circleMarker({
                        radius: 4,
                        // color: '#ff6347',
                        color: color,
                        weight: 1.2,
                        fill: true,
                        fillColor: '#effffd',
                        fillOpacity: 1,
                        code: cruiseMap.get(sn).code,
                        shipName: shipMap.get(sn).name
                    })
                },
            ]
        },
        gpsListMap.get(sn)
    ).addTo(map);
    pathPatternMap.set(sn, pathPattern);

    // 自动调整到轨迹合适的缩放级别
    // if (document.getElementById("restore").title === "暂停自动缩放") {
    //     map.fitBounds(pathPatternMap.get(sn).getBounds());
    // }
}

//初始化地图
function initMap(gpsArr) {
    BM.Config.HTTP_URL = mapHost;

    map = BM.map('mapDiv', null, {
        crs: BM.CRS.EPSG4326,
        center: [gpsArr[1], gpsArr[0]],
        // center: [gpsList[gpsList.length - 1][1], gpsList[gpsList.length - 1][0]],
        zoom: zoomLevel,
        zoomControl: true,
        minZoom: 1,
        maxZoom: 8,
        attributionControl: false
    });
    //创建一个谷歌卫星图层 ，具体API详情请参见 ：http://www.bigemap.com/offlinemaps/api/#tilelayer
    let google_satellite = BM.tileLayer('bigemap.satellite');
    google_satellite.addTo(map);

    // 地图九段线添加
    nineLine("#D3D3D3");
    // 国境线添加
    frontier("#D3D3D3");

    //鼠标对地图的监控
    map.on("moveend", function (e) {
        zoomLevel = map.getZoom();
        moveTime = Math.round(new Date());
    });

    preventGrid();
}

//地图九段线添加
function nineLine(colorLine) {
    let geoJsonArray = getNineLineJson();
    BM.geoJSON(geoJsonArray, {
        style: function (feature) {
            return {color: colorLine, weight: 1};
        }
    }).addTo(map);
}

//  国境线添加
function frontier(colorLine) {
    let frontierJsonArray = getFrontierJson();
    BM.geoJSON(frontierJsonArray, {
        style: function (feature) {
            return {color: colorLine, weight: 1};
        }
    }).addTo(map);
}

// 缩放功能
function toLessen() {
    if (pathPatternMap.length === 0) {
        return;
    }
    let stop = 0;
    //自动缩放
    $("#restore").click(function () {
        if (stop === 0) {
            document.getElementById("restore").title = "开始自动缩放";
            document.getElementById("restore").style =
                " background: url(img/typhoon/start.png); background-repeat: no-repeat; border: none;background-size: 26.8px 26.8px; background-color: #fff; border: 1px solid #ccc;background-size: 95%;background-position: center center;"
            stop = 1;
        } else {
            let pathPatternTemp = getGpsBounds();
            map.fitBounds(pathPatternTemp);
            document.getElementById("restore").title = "暂停自动缩放";
            document.getElementById("restore").style =
                " background: url(img/typhoon/stop.png); background-repeat: no-repeat; border: none;background-size: 26.8px 26.8px; background-color: #fff; border: 1px solid #ccc;background-size: 95%;background-position: center center;"
            stop = 0;
        }
    });
}

function getGpsBounds() {
    let northEastLat = 0;
    let northEastLng = 0;
    let southWestLat = 90;
    let southWestLng = 180;
    let pathPatternTemp;
    for (let i = 0; i < shipList.length; i++) {
        if (pathPatternMap.get(shipList[i].sn) === undefined) {
            continue;
        }
        pathPatternTemp = pathPatternMap.get(shipList[i].sn);
        if (northEastLat < pathPatternTemp.getBounds()._northEast['lat']) {
            northEastLat = pathPatternTemp.getBounds()._northEast['lat'];
        }
        if (northEastLng < pathPatternTemp.getBounds()._northEast['lng']) {
            northEastLng = pathPatternTemp.getBounds()._northEast['lng'];
        }
        if (southWestLat > pathPatternTemp.getBounds()._southWest['lat']) {
            southWestLat = pathPatternTemp.getBounds()._southWest['lat'];
        }
        if (southWestLng > pathPatternTemp.getBounds()._southWest['lng']) {
            southWestLng = pathPatternTemp.getBounds()._southWest['lng'];
        }
    }

    pathPatternTemp.getBounds()._northEast['lat'] = northEastLat;
    pathPatternTemp.getBounds()._northEast['lng'] = northEastLng;
    pathPatternTemp.getBounds()._southWest['lat'] = southWestLat;
    pathPatternTemp.getBounds()._southWest['lng'] = southWestLng;

    return pathPatternTemp;
}

// 跳转至对应页面
function NavigatorTo() {
    window.location.href = 'index.html';
}

// 左侧图片控制船只列表显示
function toggleList() {
    $('.hideText').css('display','none')
    // 默认为false,即显示状态
    if (toggleLeft) {
        $('.toggleList').attr('src', 'img/ship/hide.png')
        $('.toggleList').css('top', '45%')
        $('.toggleList').css('width', '1.5vw')
        $('.toggleList').css('height', '10vw')
        $('.ship').css('display', 'block')
        toggleLeft = !toggleLeft
    } else {
        $('.toggleList').attr('src', 'img/ship/show.png')
        $('.toggleList').css('width', '1.5vw')
        $('.toggleList').css('top', '35%')
        $('.toggleList').css('height', '20vw')
        $('.ship').css('display', 'none')
        $('.info').css('display', 'none')
        toggleLeft = !toggleLeft
    }
}

// 多选框点击显示航次
function showShip(sn, color) {
    // console.log(gpsListMap,'gpsListMap map对象')
    // 点击船只出现对应sn和color
    // console.log(sn, color)
    // console.log(shipObj.get(sn))
    if(shipObj.get(sn)){


        // 默认为false，即显示状态



        if (pathPatternMap.get(sn) !== undefined) {
            pathPatternMap.get(sn).remove();
            pathPatternMap.delete(sn);
        }
        //清空航线
        let pathPattern = BM.polylineDecorator(
            sn,
            {
                patterns: []
            },
            sn
        ).addTo(map);
        pathPatternMap.set(sn, pathPattern);

        shipObj.set(sn,false)
    }
    else{


        if (pathPatternMap.get(sn) !== undefined) {
            pathPatternMap.get(sn).remove();
            pathPatternMap.delete(sn);
        }
        //绘制航线
        let pathPattern = BM.polylineDecorator(
            coordinateMap.get(sn),
            {
                patterns: [
                    // 轨迹
                    {
                        offset: 5,
                        repeat: 15,
                        symbol: BM.Symbol.dash({
                            pixelSize: 9,
                            pathOptions: {
                                // color: '#ff6347',
                                color: color,
                                weight: 1.5
                            }
                        })
                    },
                    // 航线箭头
                    {
                        offset: 43,
                        repeat: 70,
                        symbol: BM.Symbol.arrowHead({
                            headAngle: 40,
                            pixelSize: 15,
                            // color: '#ff6347',
                            color: color,
                            fillOpacity: 1,
                            weight: 1
                        })
                    },
                    // 航线经过的点
                    {
                        offset: 0,
                        repeat: 70,
                        symbol: BM.Symbol.circleMarker({
                            radius: 4,
                            // color: '#ff6347',
                            color: color,
                            weight: 1.2,
                            fill: true,
                            fillColor: '#effffd',
                            fillOpacity: 1,
                            code: cruiseMap.get(sn).code,
                            shipName: shipMap.get(sn).name
                        })
                    },
                ]
            },
            gpsListMap.get(sn)
        ).addTo(map);
        pathPatternMap.set(sn, pathPattern);




        shipObj.set(sn,true)
    }
}




