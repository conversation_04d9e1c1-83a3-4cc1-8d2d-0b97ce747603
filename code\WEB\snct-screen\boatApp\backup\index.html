<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>智慧船情</title>
    <link rel="stylesheet" type="text/css" href="css/main/index.css"/>
    <link rel="stylesheet" href="js/layui-v2.5.6/layui/css/layui.css"/>
    <link rel="stylesheet" href="css/bigemap/MarkerCluster.Default.css" type="text/css"/>
    <link rel="stylesheet" href="css/bigemap/meteorology.css" type="text/css"/>

    <script src="js/other/echarts.min.js"></script>
    <script src="js/other/jquery/jquery-3.3.1.min.js"></script>
    <script src="js/bigemap/bootstrap.min.js"></script>
    <script src="js/common/host.js"></script>
    <script src="js/common/mapCommon.js"></script>
    <script src="js/common/commonFun.js"></script>

    <script type="text/javascript">
        let nowTime = new Date().getTime();
        document.write('<link href="css/mapCss/style2.css?timestamp=' + nowTime + '" rel="stylesheet" type="text/css"/>');
        document.write('<link href="' + mapHost + '/bigemap.js/v2.1.0/bigemap.css" rel="stylesheet" type="text/css"/>');
        document.write('<script src="' + mapHost + '/bigemap.js/v2.1.0/bigemap.js" type="text/javascript" ><\/script>');
    </script>
    <script type="text/javascript" src="js/layui-v2.5.6/layui/layui.js"></script>
    <script type="text/javascript" src="js/bigemap/arrow/patternUtils.js"></script>
    <script type="text/javascript" src="js/bigemap/arrow/symbol.js"></script>
    <script type="text/javascript" src="js/bigemap/arrow/polyline_arrow.js"></script>
    <script type="text/javascript" src="js/bigemap/arrow/rotate_marker.js"></script>
    <script type="text/javascript" src="js/bigemap/track/moveMarker.js"></script>
    <script type="text/javascript" src="js/bigemap/track/bm.geometryutil.js"></script>
    <script type="text/javascript" src="js/bigemap/bm.markercluster-src.js"></script>

    <script src="js/other/jquery/jquery.cookie.js"></script>
    <script src="js/common/auth.js"></script>
</head>
<body>
<div class="background" id="app" @click="closeOpen">
    <img src="img/index/bg01.png" alt="">
    <div class="mainWrap">
        <!-- 项目名 -->
        <div class="subtns" id="submenuDiv"></div>
        <div class="subtns1"></div>
        <div class="submenu" style="display: none;">
            <!--<a href="webConning.html">船舶信息显示系统</a>-->
            <a href="show2.html">视频监控</a>
            <a href="mapgis.html">实时轨迹</a>
            <a href="ship.html">返回</a>
        </div>
        <div class="flex">
            <div class="mainWrapLeft">
                <div class="DateBox flex col leftYmd"></div>
                <div class="title" style="background: url(./img/index/title1.png) no-repeat;"></div>
                <div style="background: url(./img/index/block.png) center center/100% 100% no-repeat;">
                    <div class="sailBlock">
                        <h3 id="processLine"></h3>
                        <div class="process">
							<span id="shipImg">
								<img src="img/index/ship.png">
                                <p></p>
							</span>
                        </div>
                        <div class="sailMileage">
                            <p>行驶里程</p>
                        </div>
                        <div class="sailNo" id="cruiseCode">航次</div>
                        <div class="sailDate">
                            <span id="date"></span>
                            <span id="mileage">nm</span>
                        </div>
                    </div>
                </div>

                <div class="title" style="background: url(./img/index/title1.png) no-repeat;margin-top: 10px;">船舶基本信息</div>
                <div style="background: url(./img/index/block.png) center center/100% 100% no-repeat;">
                    <div class="sailBlock" style="padding-left: 6%;">
                        <div class="flex sailBlockItem">
                            <h4>纬&nbsp;&nbsp;&nbsp;&nbsp;度</h4>
                            <p style="margin-left: 0.5vw;" id="lat"></p>
                        </div>
                        <div class="flex sailBlockItem">
                            <h4>经&nbsp;&nbsp;&nbsp;&nbsp;度</h4>
                            <p id="lon"></p>
                        </div>
                        <div class="flex sailBlockItem j-center" id="utcT" style="padding-top: 0.5vw;">
                        </div>
                    </div>
                </div>

                <div style="background: url(./img/index/block.png) center center/100% 100% no-repeat;">
                    <div class="awsData flex">
                        <div class="directiveFn">
                            <div class="directiveThat"></div>
                            <div class="directiveText">
                                <h5>船向</h5>
                                <p id="hehdt">°</p>
                            </div>
                        </div>
                        <div class="directiveFn1">
                            <div class="directiveThat1" id="shipS"></div>
                            <div class="directiveText" style="padding-top: 38%;">
                                <h5>船速</h5>
                                <p id="shipSpeed">节</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div style="background: url(./img/index/block.png) center center/100% 100% no-repeat;">
                    <div class="awsData flex">
                        <div class="directiveFn">
                            <div class="directiveThat2" id="trueR"></div>
                            <div class="directiveText">
                                <h5>风向</h5>
                                <p id="trueW"></p>
                            </div>
                        </div>
                        <div class="directiveFn2">
                            <div class="directiveThat1" id="wSpeed"></div>
                            <div class="directiveText" style="padding-top: 38%;">
                                <h5>风速</h5>
                                <p id="trueWindS">节</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!--<div class="title" style="margin-top: 1.8vw;">水温实时数据</div>-->
                <!--<div class="awsData aws" style="height: 19.2vw;width: 104%">-->
                <!--<div id="co2Chart" style="width: 100%; height: 100%;"></div>-->
                <!--</div>-->
            </div>
            <!--left-->

            <div class="mainWrapCenter">
                <div id="restore" class="huifu btn_weather1 div_btn" title="暂停自动缩放"
                     style=" background-size: 95%;border: 1px solid #ccc; background-color: #fff;"></div>
                <!-- 地图底图 -->
                <div id="map" style="cursor:default; border: 2px solid rgb(14, 198, 196);">
                </div>
            </div>

            <div class="mainWrapRight">
                <div class="DateBox flex a-center j-end"
                     style="color: #23cefd; padding-bottom: 3.1vw; position: relative; right: -5%; padding-top: 1.1vw; margin-right: -0.4vw; height: auto;">
                    <div class="rightYmd" style="font-size: 1vw;margin-top: 0.2vw;"></div>
                </div>

                <!--<div class="title">船舶基本信息</div>-->
                <!--<div class="sailBlock" style="padding-left: 6%;">-->
                    <!--<div class="flex sailBlockItem">-->
                        <!--<h4>纬&nbsp;&nbsp;&nbsp;&nbsp;度</h4>-->
                        <!--<p style="margin-left: 0.5vw;" id="lat"></p>-->
                    <!--</div>-->
                    <!--<div class="flex sailBlockItem">-->
                        <!--<h4>经&nbsp;&nbsp;&nbsp;&nbsp;度</h4>-->
                        <!--<p id="lon"></p>-->
                    <!--</div>-->
                    <!--<div class="flex sailBlockItem j-center" id="utcT" style="padding-top: 0.5vw;">-->
                    <!--</div>-->
                <!--</div>-->

                <!--<div class="title" style="margin-top: 1.92vw;">船舶行驶信息</div>-->
                <!--<div class="awsData flex">-->
                    <!--<div class="directiveFn">-->
                        <!--<div class="directiveThat"></div>-->
                        <!--<div class="directiveText">-->
                            <!--<h5>船向</h5>-->
                            <!--<p id="hehdt">°</p>-->
                        <!--</div>-->
                    <!--</div>-->
                    <!--<div class="directiveFn1">-->
                        <!--<div class="directiveThat1" id="shipS"></div>-->
                        <!--<div class="directiveText" style="padding-top: 38%;">-->
                            <!--<h5>船速</h5>-->
                            <!--<p id="shipSpeed">节</p>-->
                        <!--</div>-->
                    <!--</div>-->
                <!--</div>-->
                <!--<div class="title" style="margin-top: 1.96vw;">船舶气象信息</div>-->
                <!--<div class="awsData flex">-->
                    <!--<div class="directiveFn">-->
                        <!--<div class="directiveThat2" id="trueR"></div>-->
                        <!--<div class="directiveText">-->
                            <!--<h5>风向</h5>-->
                            <!--<p id="trueW"></p>-->
                        <!--</div>-->
                    <!--</div>-->
                    <!--<div class="directiveFn2">-->
                        <!--<div class="directiveThat1" id="wSpeed"></div>-->
                        <!--<div class="directiveText" style="padding-top: 38%;">-->
                            <!--<h5>风速</h5>-->
                            <!--<p id="trueWindS">节</p>-->
                        <!--</div>-->
                    <!--</div>-->
                <!--</div>-->
            </div>
        </div>

        <ul class="pictureList">
            <li>
                <div class="footItems flex col" id="flex1">
                    <div class="footItems_title" id="imgTitle0" style="background: url(./img/index/title2.png) center center/93% 100% no-repeat;">XXXX</div>
                    <div class="footItems_cont">
                        <img id="img0" style="border: 2px solid #1cb2ff;">
                    </div>
                </div>
            </li>
            <li>
                <div class="footItems flex col" id="flex2">
                    <div class="footItems_title" id="imgTitle1" style="background: url(./img/index/title2.png) center center/93% 100% no-repeat;">XXXX</div>
                    <div class="footItems_cont">
                        <img id="img1" style="border: 2px solid #1cb2ff;">
                    </div>
                </div>
            </li>
            <li style="padding-left: 2.65vw">
                <div class="footItems flex col" id="flex3">
                    <div class="footItems_title" id="imgTitle2" style="background: url(./img/index/title2.png) center center/93% 100% no-repeat;">XXXX</div>
                    <div class="footItems_cont">
                        <img id="img2" style="border: 2px solid #1cb2ff;">
                    </div>
                </div>
            </li>
            <li style="padding-left: 2.37vw">
                <div class="footItems flex col" id="flex4">
                    <div class="footItems_title" id="imgTitle3" style="background: url(./img/index/title2.png) center center/93% 100% no-repeat;">XXXX</div>
                    <div class="footItems_cont">
                        <img id="img3" style="border: 2px solid #1cb2ff;">
                    </div>
                </div>
            </li>
        </ul>


    </div>

    <div class="backgroundFooter flex a-center j-center" style="letter-spacing: 0.5vw;">
        自然资源部第二海洋研究所
    </div>

</div>
<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">

            <div class="modal-body">
                <div id="modalHeard"
                     style="width:100%;height: 2.5vw;background: rgba(0,16,27,0.7);display: none;position: absolute;">
                    <img class="close" data-dismiss="modal" aria-hidden="true" src="img/picture/close.png" title="关闭"/>
                    <img class="play" src="img/picture/play.png" title="播放历史"/>
                </div>
                <img class="camera"/>
            </div>
        </div>

    </div>
</div>

<script src="js/main/index.js"></script>
</body>
</html>
