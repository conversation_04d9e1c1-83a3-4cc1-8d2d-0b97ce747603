//建立的连接
let ws = null;
//是否真正建立连接
let lockReconnect = false;
//30秒一次心跳
let timeout = 15 * 60 * 1000;
//心跳心跳倒计时
let timeoutObj = null;
//心跳倒计时
let serverTimeoutObj = null;
let timeoutNum = null;

//连接websocket
function createWebSocket(wsUrl, handleWsData) {
    ws = new WebSocket(wsUrl);
    ws.onopen = function () {
        console.log('WebSocket已打开: ');
        //开启心跳
        start();
    };
    ws.onmessage = function (e) {
        let wsData = JSON.parse(e.data);
        handleWsData(wsData);
        reset();
    };

    ws.onclose = function (e) {
        console.log('WebSocket关闭: ');
        console.log(e);
        //重连
        reconnectWs(wsUrl, handleWsData);
    };

    ws.onerror = function (e) {
        console.log('WebSocket发生错误: ');
        console.log(e);
        //重连
        reconnectWs(wsUrl, handleWsData);
    };

    // 监听窗口关闭事件，当窗口关闭时，主动去关闭websocket连接，防止连接还没断开就关闭窗口，server端会抛异常。
    window.onbeforeunload = function () {
        ws.close();
    };
}

//重新连接
function reconnectWs(wsUrl, handleWsData) {
    if (lockReconnect) {
        return;
    }
    lockReconnect = true;
    timeoutNum && clearTimeout(timeoutNum);
    timeoutNum = setTimeout(function () {
        //新连接
        createWebSocket(wsUrl, handleWsData);
        lockReconnect = false;
    }, 5000);
}

//开启心跳
function start() {
    timeoutObj && clearTimeout(timeoutObj);
    serverTimeoutObj && clearTimeout(serverTimeoutObj);
    timeoutObj = setTimeout(function () {
        //这里发送一个心跳，后端收到后，返回一个心跳消息，
        ws.send("heartCheck");
        serverTimeoutObj = setTimeout(function () {
            //超时关闭
            ws.close();
        }, timeout);
    }, timeout);
}

//重置心跳
function reset() {
    //清除时间
    clearTimeout(timeoutObj);
    clearTimeout(serverTimeoutObj);
    //重启心跳
    start();
}

function closeWs() {
    lockReconnect = true;
    ws.close();
}

function restartWs(wsUrl, handleWsData) {
    lockReconnect = false;
    //重连
    createWebSocket(wsUrl, handleWsData);
}
