<template>
  <div class="login">
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
      <h3 class="title">SNCT智慧船情大屏系统</h3>

      <el-form-item prop="username">
        <el-input
          ref="username"
          v-model="loginForm.username"
          type="text"
          auto-complete="off"
          placeholder="账号"
          name="username"
          tabindex="1"
        >
          <i slot="prefix" class="el-icon-user el-input__icon input-icon" />
        </el-input>
      </el-form-item>

      <el-form-item prop="password">
        <el-input
          ref="password"
          v-model="loginForm.password"
          :type="passwordType"
          auto-complete="off"
          placeholder="密码"
          name="password"
          tabindex="2"
          @keyup.enter.native="handleLogin"
        >
          <i slot="prefix" class="el-icon-lock el-input__icon input-icon" />
          <i
            slot="suffix"
            :class="passwordType === 'password' ? 'el-icon-view' : 'el-icon-hide'"
            class="show-pwd"
            @click="showPwd"
          />
        </el-input>
      </el-form-item>

      <el-form-item style="width:100%;">
        <el-button
          :loading="loading"
          size="medium"
          type="primary"
          style="width:100%;"
          @click.native.prevent="handleLogin"
        >
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 滑块验证组件 -->
    <Verify
      ref="verify"
      :captcha-type="captchaType"
      :mode="'pop'"
      @success="onVerifySuccess"
      @error="onVerifyError"
    />

    <!--  底部  -->
    <div class="el-login-footer">
      <span>Copyright © 2018-2025 SNCT All Rights Reserved.</span>
    </div>
  </div>
</template>

<script>
import { dataModule } from "../../utils/webSocket.js";
import { login } from '@/api/login'
import { setToken,setUtype } from '@/utils/auth'
import Verify from '@/components/Verifition/Verify.vue'

export default {
  name: 'Login',
  components: {
    Verify
  },
  data() {
    return {
      loginForm: {
        username: '',
        password: '',
        type: 1,
        code: ''
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "请输入您的账号" }
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" }
        ]
      },
      loading: false,
      passwordType: 'password',
      captchaType: 'blockPuzzle', // 滑块验证类型
      verifyToken: '', // 验证码token
      redirect: undefined
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true
    }
  },
  mounted() {

    console.log('登录页面########:', dataModule.D0A01)
    
    if (this.loginForm.username === '') {
      this.$refs.username.focus()
    } else if (this.loginForm.password === '') {
      this.$refs.password.focus()
    }
  },
  methods: {
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },

    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          // 显示滑块验证
          this.$refs.verify.clickShow = true
        } else {
          console.log('表单验证失败')
          return false
        }
      })
    },

    // 滑块验证成功回调
    onVerifySuccess(params) {
      console.log('验证成功:', params)
      this.verifyToken = params.captchaVerification
      this.doLogin()
    },

    // 滑块验证失败回调
    onVerifyError() {
      console.log('验证失败')
      this.$message.error('验证失败，请重试')
    },

    // 执行登录请求
    doLogin() {
      this.loading = true
      const loginData = {
        username: this.loginForm.username,
        password: this.loginForm.password,
        type: this.loginForm.type,
        code: this.verifyToken
      }

      login(loginData)
        .then(response => {
          console.log('登录响应:', response)
          if (response.code === 200) {
            const token = response.token || (response.data && response.data.token)
            const utype = response.type || (response.type && response.data.type)
            if (token) {
              setToken(token)
              setUtype(utype)
              console.log('登录成功');
              //this.$message.success('登录成功')
              if('U01'==utype){
                this.$router.push({ path: this.redirect || '/index' }).catch(()=>{})
              }else{
                this.$router.push({ path: this.redirect || '/comindex' }).catch(()=>{})
              }
            } else {
              this.$message.error('登录失败：未获取到token')
            }
          } else if (response.repCode === '0000') {
            const token = response.repData && response.repData.token
            const utype = response.repData && response.repData.utype
            if (token) {
              setToken(token)
              setUtype(utype)
              console.log('登录成功');
              //this.$message.success('登录成功')
              if('U01'==utype){
                this.$router.push({ path: this.redirect || '/index' }).catch(()=>{})
              }else{
                this.$router.push({ path: this.redirect || '/comindex' }).catch(()=>{})
              }
            } else {
              this.$message.error('登录失败：未获取到token')
            }
          } else {
            const errorMsg = response.msg || response.repMsg || '登录失败'
            this.$message.error(errorMsg)
          }
        })
        .finally(() => {
          this.loading = false
          this.verifyToken = ''
        })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background: url('~@/assets/img/ship.png') no-repeat center center;
  background-size: cover;
}

.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #707070;
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 350px;
  padding: 25px 25px 5px 25px;

  .el-input {
    height: 38px;
    input {
      height: 38px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }

  .show-pwd {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #c0c4cc;
    font-size: 16px;

    &:hover {
      color: #409eff;
    }
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
</style>