.ol-popup {
    position: absolute;
    background-color: #91B4C8;
    -webkit-filter: drop-shadow(0 1px 4px rgba(0, 0, 0, 0.2));
    filter: drop-shadow(0 1px 4px rgba(0, 0, 0, 0.2));
    padding: 15px;
    border-radius: 10px;
    border: 1px solid #cccccc;
    bottom: 12px;
    left: -50px;
    min-width: 210px;
   
}
.leaflet-control-zoom{
    display: none;
}
.leaflet-control-scale-line{
    display: none;
}

.leaflet-control-attribution-elane{
    display: none;
}
.ol-zoom {
    display: none;
}
.ol-popup:after,
.ol-popup:before {
    top: 100%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
}

.ol-popup:after {
    border-top-color: white;
    border-width: 10px;
    left: 48px;
    margin-left: -10px;
}

.ol-popup:before {
    border-top-color: #cccccc;
    border-width: 11px;
    left: 48px;
    margin-left: -11px;
}

.ol-popup-closer {
    text-decoration: none;
    position: absolute;
    top: 2px;
    right: 8px;
    /* background-color: #409DFF; */
}

.ol-popup-closer:after {
    content: "âœ–";
}
.suspendQQ  table tbody tr{
    /* border: #286090 solid 1px; */
    color: white;
   
    
}

.textB{
    text-align:justify;text-justify:distribute-all-lines;text-align-last:justify

}
.suspendQQ  table tbody tr td{
    font-size: 2.5vh;
    /* border: #286090 solid 1px; */
    color: white;
    /* line-height: 3.6vw; */
    line-height: 2.5vw;
}
.suspendQQ table{
    margin-left: 1.5vw;
	/* margin-top: 1.6vw; */
    width: 2vw;
	opacity: 0.8;
    /* border: 3px salmon outset ; */
}
.textC{
    text-align:center;
   font-size: 2.5vh !important;
}
.measuretip {
    position: relative;
    background-color: white;
    opacity: 0.7;
    border-radius: 3px;
    padding: 10px;
    font-size: 12px;
    cursor: default;
}

.vjs-modal-dialog {
    display: none;
}

.vjs-control-bar {
    display: none;
}

.vjs-big-play-button {
    display: none;
}
.back{
    width:224px;
    position: absolute;
    z-index: 11;
    right:14px;
    top:13px;
    background-color: white;
    opacity: 0.7;
    border-radius: 3px;
    padding: 10px;
    font-size: 12px
}
.sailingShow {
  
    /* z-index: 99; */
    width: 26.6vw;
    height: 57vw;
    top: -0.15vw;
    position: absolute;
    left: 74.5vw;
    /* font-size: 12px; */
    /* border: tomato 1px solid; */
    text-align:center;
    display:none;
}
.imgbg{
    width: 16.7vw;
}
.showbacktext{
    width: 448px;
    height: 105px; 
    position: relative;
    top:320px;
    background-color: #337AB7;
    opacity: 0.8;
    border-radius: 3px;
    right: -4px;
    display:none;
}
.backtext{
    width: 448px;
     height: 105px; 
     position: relative;
     top:320px;
     background-color: white;
     opacity: 0.8;
     border-radius: 3px;
     right: -4px;  
}
.btn-group{
    position:absolute;
    top: 0.5vw;
    left: 89.6vw;
}
.btn-xss{
    padding: 0.1vw 0.2vw;
    font-size: 0.85vw;
    line-height: 2vh;
    width: 4.6vw;
    height: 1.3vw;
    border-radius: 0.2vw;
    background-color:#0CBFC2; 
    /* margin-left: -0.2vw; */
}
#shipText{
    width: 21.5vw;
  /* border: 3px salmon outset ; */
    position: absolute;
    top: 0.5vw;
    /* background-color: white; */
    padding:0.2vw;
    opacity: 0.8;
    right: 2vw;    
    text-align:center;
   
}
.btnselect{
    /* margin-left:105px; */
    width: 5.2vw;
    background-color: #0CBFC2;
    /* opacity: 0.8; */
    height: 2.5vw;
    font-size: 2vh;
    /* border-radius: 50% 50%; */
    margin: 0.5vw 5.7vw;
}
.inputtime{
    width: 4vw;
    height: 1.5vw;
    text-align: center;
    font-size: 1.5vh;
}
.shipsShow{
    width: 252px;
    height: 135px;
    position: absolute;
    z-index: 11;
    left:80%;
    top: 1%;
    line-height: 20PX;
    background-color: #286090;
    /* opacity: 0.8; */
    border-radius: 3px;
    padding: 10px;    
    display:none;
    /* text-align:center; */
}
.gunds{
    width: 16.5vw; 
    height:2.2vw ; 
    /* margin-left: 3.2vw;
    margin-top: 0.5vw; */
}
.but_sty{ border:0px; width:80px; height:30px; background-color:#333; font-size:12px; color:#fff;}
.buttonGroup{
    margin-left: 4.5vw;
    width: 100%;
    height: 1.2vw;
    position: absolute;
    top: 44.3vw;
}
#backButton{
    cursor: pointer;
    font-size:1.5vh;
	width: 2vw;
	height: 2vw;
	background-position:center center;
}
#startButton{
    cursor: pointer;
    font-size:1.5vh;
    /* margin-top: -25px; */
    margin-top: 0.3vw;
    /* width: 4.5vw; */
	width: 2.5vw;
	height: 2.5vw;
    /* margin-right:6vw; */
	/* margin-right:2vw; */
	background-position:center center;
	/* opacity: 0.01; */
    /* background-color: #286090; */

}
#pauseButton{
    cursor: pointer;
    font-size: 1.5vh;
    /* margin-top: 0.5vw; */
    /* margin-right:-0.6vw; */
    /* position: absolute; */
    /* left: 15vw; */
	/* left: 16vw; */
    /* top: 24.88vw; */
    width: 2vw;
    height: 2vw;
	background-position:center center;
	/* opacity: 0.01; */
    /* background-color: #286090; */
}

.seleship{
    width: 8vw;
     height:2vw;
      text-align:center;
      font-size: 2vh; 
      margin: 1vw;
      color: black
}
.button-close{
    cursor: pointer;
}

.button1{
    position:absolute;
    top: 0.5vw;
    left: 94.8vw;
  
}
.button2{
    left: 2676%;
    top: 40px;
  
}
.button3{
    left: 1618px;
    top: 60px
}
#imgtu1{
	position: absolute;width: 2.5vw;height: 2.5vw;z-index: 80;
}

#imgtu2{position: absolute;width: 2.5vw;height: 2.5vw;display:none;z-index: 80;}
#imgtu3{position: absolute;width: 2.5vw;height: 2.5vw;left: 16.5vw;z-index: 80;}
#imgtu4{position: absolute;width: 2.5vw;height: 2.5vw;left: 16.5vw; display:none;z-index: 80;}
*{margin:0;padding:0;list-style-type:none;}
/* suspend */
.suspend{width:2.2vw;height:50vw;position:fixed;top:5%;right:0;overflow:hidden;z-index:9999;}
.suspend dl{width:23vw;height:50vw;padding-left:2.1vw;}
.suspend dl dt{width:2.2vw;height:50vw;position:absolute;top:0;left:0;cursor:pointer;}
.suspend dl dd.suspendQQ {
    width:19.5vw;
    /* width:21vw; */
    height:50vw;
    /* height:47.5vw;  */
    display:block;overflow:hidden;}
.showchange1{
    display: none;
}
.showchange2{
    display: block;
}
/* 去除默认样式 */
input[type=range] {
    -webkit-appearance: none;
    height: 0.6vw;
    width: 16.5vw;
    border: radius 0.5vw;; /*这个属性设置使填充进度条时的图形为圆角*/
}
input[type=range]::-webkit-slider-thumb {
    -webkit-appearance: none;
}  
  /*轨道添加样式  */
input[type=range]::-webkit-slider-runnable-track {
    /* background-color: #286090; */
    border-color: #0CBFC2;
    height: 0.7vw;
    border-radius: 10px; /*将轨道设为圆角的*/
    box-shadow: 0 0.01vw 0.01vw #def3f8, inset 0 .125em .125em #0d1112; /*轨道内置阴影效果*/
}


/* 原始的控件获取到焦点时，会显示包裹整个控件的边框，所以还需要把边框取消。 */

input[type=range]:focus {
    outline: none;
}


 /* 给滑块(thumb)添加样式 */

input[type=range]::-webkit-slider-thumb {
    -webkit-appearance: none;
    height: 1.5vw;
    width: 1.5vw;
    /* color: #0d1112;
    background-color: #209c96;
    border-color: #17948e; */
    margin-top: -0.3vw; /*使滑块超出轨道部分的偏移量相等*/
    background: #ffffff; 
    border-radius: 50%; /*外观设置为圆形*/
    border: solid 0.125em rgba(205, 224, 230, 0.5); /*设置边框*/
    box-shadow: 0 .125em .125em #3b4547; /*添加底部阴影*/
}
