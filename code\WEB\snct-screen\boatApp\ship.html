<!DOCTYPE html>
<html lang="en">
<head>
    <title>智慧船情</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=0.5, maximum-scale=2.0, user-scalable=yes" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <script type="text/javascript" src="js/other/jquery/jquery-3.3.1.min.js"></script>
    <!--    反向代理设置-->
    <script src="js/common/host.js"></script>
    <link rel="stylesheet" href="css/main/ship.css">
    <link rel="stylesheet" href="css/bigemap/MarkerCluster.Default.css" type="text/css"/>
    <link rel="stylesheet" href="css/bigemap/meteorology.css" type="text/css"/>
    <script type="text/javascript">
        let nowTime = new Date().getTime();
        document.write('<link href="css/mapCss/style2.css?timestamp=' + nowTime + '" rel="stylesheet" type="text/css"/>');
        document.write('<link href="' + mapHost + '/bigemap.js/v2.1.0/bigemap.css" rel="stylesheet" type="text/css"/>');
        document.write('<script src="' + mapHost + '/bigemap.js/v2.1.0/bigemap.js" type="text/javascript" ><\/script>');
    </script>
    <script type="text/javascript" src="js/bigemap/bootstrap.min.js"></script>
    <script type="text/javascript" src="js/bigemap/arrow/patternUtils.js"></script>
    <script type="text/javascript" src="js/bigemap/arrow/symbol.js"></script>
    <script type="text/javascript" src="js/bigemap/arrow/polyline_arrow.js"></script>
    <script type="text/javascript" src="js/bigemap/arrow/rotate_marker.js"></script>
    <script type="text/javascript" src="js/bigemap/track/moveMarker.js"></script>
    <script type="text/javascript" src="js/bigemap/track/bm.geometryutil.js"></script>
    <script type="text/javascript" src="js/bigemap/bm.markercluster-src.js"></script>
    <script type="text/javascript" src="js/other/jquery/jquery.cookie.js"></script>
    <script src="js/common/auth.js"></script>
    <script src="js/common/commonFun.js"></script>
    <script src="js/common/mapCommon.js"></script>
    <script>
        var html = document.querySelector('html');
        function setRem() {
            var screenWidth = window.innerWidth; //获取屏幕宽
            var rem1 = screenWidth / 100; //屏幕的宽度/设计稿占满全屏幕所需的rem数量，就可以得到1rem为多少像素。
            html.style.fontSize = rem1 + 'px';
        };
        setRem();
        window.onresize = function() {
            setRem();
        }
    </script>

</head>
<body>
<div style="width: 100%">

    <div class="head">
        <div>
            <div class="leftTop"></div>
            <div  class="leftYmd"></div>
            <div class="localTime">本地时间</div>
        </div>
        <div class="title">智慧船情</div>
        <div>
            <div class="serverTime"></div>
            <div class="serveTitle">UTC</div>
            <div class="rightYmd"></div>
        </div>

    </div>

    <!-- 地图底图 -->
    <div style="width: 100%; height: 60vw; position: fixed;">


        <!--<div style="z-index: 9;position: absolute;right: 1vw;top: 1vw;"><span style="color: #ff6347;">工程船1</span></div>-->
        <!--<div style="z-index: 9;position: absolute;right: 1vw;top: 2vw;"><span style="color: #0ecfbf;">工程船2</span></div>-->

        <!-- 地图底图 -->
        <div id="mapDiv"
             style="position: absolute;top: 0;bottom: 0;width: 100%;height: 100%;padding: 0 0;cursor:default"></div>

    </div>

    <div>
        <img    class="toggleList" src="img/ship/hide.png" onclick="toggleList()">
    </div>

    <div class="ship">
        <h1>
            <span style="font-size: 18px">船只列表</span>
            <img src="img/ship/shipTop.png" style="display: inline-block;vertical-align: middle;"></h1>
        <div id="shipDiv"></div>

        <div class="hideText" onclick="showInfo()" >
            <img src="img/ship/hideInfo.png"  style="display: inline-block;vertical-align: top;width: 20px;height: 20px" >
            <span style="font-size: 18px;color: #fff;margin: 2px 7px;line-height: 20px">隐&nbsp;藏</span>
        </div>
    </div>

    <div class="info"  >
        <div class="baseInfo">
            <div>
                <h1 style="font-size: 18px">
                    <span id="baseInfo"></span>
                    <img src="img/ship/shipTop.png" style="display: inline-block;vertical-align: middle;">
                    <img src="img/ship/enterInfo.png" style="display: inline-block;vertical-align: bottom;cursor: pointer;float: right" onclick="NavigatorTo()">
                </h1>
            </div>
            <div>
                <span class="infoTitle">船&nbsp;&nbsp;名</span>
                <span id="shipName" ></span>
            </div>
            <div>
                <span class="infoTitle">状&nbsp;&nbsp;态</span>
                <span id="shipStatus" ></span>
            </div>
            <div>
                <span class="infoTitle">航&nbsp;&nbsp;速</span>
                <span id="shipSpeed" >暂无数据</span>
            </div>
            <div>
                <span class="infoTitle">纬&nbsp;&nbsp;度</span>
                <span id="latitude" >暂无数据</span>
            </div>

            <div>
                <span class="infoTitle">经&nbsp;&nbsp;度</span>
                <span id="longtitude" >暂无数据</span>
            </div>
            <div>
                <span class="infoTitle">UTC</span>
                <span id="shipTime" >暂无数据</span>
            </div>
        </div>
        <div class="imgInfo" style="margin-bottom: 0.5vw;position: relative">
            <img  id="img0"  src="" style="width: 90%;height: 90%;position: absolute">
            <div  id="imgText0"   style="position: absolute;z-index: 99;left: 0;bottom: 0;color: #fff;font-size: 14px;left: 13px;bottom: 10px"></div>
        </div>
        <div class="imgInfo" style="position:relative;">
            <img  id="img1"  src="" style="width: 90%;height: 90%;position: absolute">
            <div  id="imgText1"   style="position: absolute;z-index: 99;left: 0;bottom: 0;color: #fff;font-size: 14px;left: 13px;bottom: 10px"></div>
        </div>



    </div>


    <footer>
        <p>Copyright©智慧船情</p>
    </footer>

</div>

<script type="text/javascript" src="js/other/swiper.min.js"></script>
<script src="js/common/commonFun.js"></script>
<script src="js/main/ship.js"></script>
</body>
</html>
